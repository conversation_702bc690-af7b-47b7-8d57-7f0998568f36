<script setup>
import BaseIcon from '@/components/base/BaseIcon.vue';
import Dropdown from '@/components/Dropdown.vue';
import Table from '@/components/Table.vue';
import { usePaginationcopy } from '@/composables/usePaginationcopy';
import { openModal } from '@customizer/modal-x';
import { mdiDeleteAlert, mdiDotsVertical, mdiMagnify, mdiPencil } from '@mdi/js';
import { getAllManufacturers, removeManufacturer } from '../api/ManufacturerApi';
import { useManufacturers } from '../store/manufacturerStore';
import TableRowSkeleton from '@/skeletons/TableRowSkeleton.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { toasted } from '@/utils/utils';
import Button from '@/components/Button.vue';
import DefaultPage from "@/components/DefaultPage.vue";

const manufacturerStore = useManufacturers();
const pagination = usePaginationcopy({
    store: manufacturerStore,
    cb: (params) => getAllManufacturers(params),
});

const removeReq = useApiRequest();
function remove(id) {
  openModal('Confirmation', {
    title: 'Remove Manufacturer',
    message: 'Are you sure you want to delete this manufacturer?'
  },
  (res) => {
    if (res) {
      removeReq.send(() => removeManufacturer(id),
        (res) => {
          if (res.success) {
            manufacturerStore.remove(id);
          }
          toasted(res.success, 'Manufacturer removed successfully', res.error);
        })
    }
  });
}

function editManufacturer(manufacturer) {
  openModal('EditManufacturer', manufacturer);
}
</script>

<template>
  <DefaultPage>
    <div class="flex justify-between items-center mb-6">
      <div class="flex border rounded px-2 items-center">
        <input
          class="border-0 px-2 h-10 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
          placeholder="Search manufacturers"
          v-model="pagination.search.value"
          @keydown.enter="pagination.fetch()"
        />
        <BaseIcon :path="mdiMagnify" :size="20" />
      </div>
      <div>
        <Button
          type="primary"
          @click="openModal('AddManufacturer')"
          class="flex items-center gap-2 px-4 py-2 rounded"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M12 5V19M5 12H19"
              stroke="white"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              fill="white"
            />
          </svg>
          <span class="truncate px-3">Add Manufacturer</span>
        </Button>
      </div>
    </div>
    <Table
      :pending="pagination.pending.value"
      :headers="{
        head: [
          'Manufacturer Name',
          'Contact Person',
          'Phone',
          'Email',
          'Address',
          'Country',
          'Actions',
        ],
        row: [
          'manufacturerName',
          'contactPerson',
          'phone',
          'email',
          'address',
          'country',
        ]
      }"
      :Fallback="TableRowSkeleton"
      :rows="manufacturerStore.manufacturers"
    >
      <template #actions="{ row }">
        <Dropdown v-slot="{ setRef, toggleDropdown }">
          <button @click="toggleDropdown" class="rounded-full p-1">
            <BaseIcon :path="mdiDotsVertical" :size="24" />
          </button>
          <div class="flex shadow-lg p-1 border rounded flex-col gap-2 bg-white" :ref="setRef">
            <button @click="editManufacturer(row)" class="flex items-center gap-2 hover:bg-gray-100 p-1 rounded">
              <BaseIcon :path="mdiPencil" :size="20" />
              <span>Edit</span>
            </button>
            <button @click="remove(row.manufacturerUuid)" class="flex items-center gap-2 hover:bg-gray-100 p-1 rounded">
              <BaseIcon :path="mdiDeleteAlert" :size="20" class="text-red-500" />
              <span>Delete</span>
            </button>
          </div>
        </Dropdown>
      </template>
    </Table>
  </DefaultPage>
</template>