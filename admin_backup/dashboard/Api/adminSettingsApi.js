import { getApi } from "@/utils/utils"

const api = getApi('/admin')

export function updateInventroyStatus(id, status = true) {
	return api.addAuthenticationHeader().put('/lock-inventory', {},  {
		params: {
			lock: status,
			systemSettingUuid: id
		}
	})
}

export function openInventroy(id) {
	return api.addAuthenticationHeader().put('/lock-inventory', {
		look: false,
		systemSettingUuid: id
	})
}

export function getSystemStatus() {
	return api.addAuthenticationHeader().get('')
}