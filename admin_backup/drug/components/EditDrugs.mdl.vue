<script setup>
import { data } from "autoprefixer";
import NewFormParent from "../../role/components/NewFormParent.vue";
import DrugForm from "../form/DrugForm.vue";
import { useRouter } from "vue-router";
import { useApiRequest } from "@/composables/useApiRequest";
import { updateDrug } from "../Api/drugApi";
import { useDrugs } from "../store/drugStore";
import { useForm } from "@/new_form_builder/useForm";
import { closeModal } from "@customizer/modal-x";

const props = defineProps({
  data: {
    type: Object,
  },
});

console.log(props.data);

const drugStore = useDrugs();
// const drugss = ref(drugStore.drugs.findIndex((el) => el.drugUuid == drugUuid || {}));

const { submit } = useForm("EditDrugForm");
const drugs = useApiRequest();
const router = useRouter();

function drugsUpdate({ values }) {
  drugs.send(
    () => updateDrug(props.data?.drugUuid, values),
    (res) => {
      if (res.success) {
        drugStore.update(props.data?.drugUuid, {
          ...props.data,
          ...values,
        });
        toasted(res.success, "Drug Created", res.error);
      }
      closeModal();
    }
  );
}
</script>

<template>
  <div class="bg-black/50 min-h-full w-full p-10 grid place-items-center">
    <NewFormParent size="lg" title="Edit Drug">
      <DrugForm :onSubmit="drugsUpdate" :drug="props.data" />
    </NewFormParent>
  </div>
</template>
