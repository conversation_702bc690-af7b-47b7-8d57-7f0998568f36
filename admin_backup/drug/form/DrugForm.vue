<script setup>
import FormSubmitButton from "@/components/FormSubmitButton.vue";
import Input from "@/components/new_form_elements/Input.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import Form from "@/new_form_builder/Form.vue";
import { useRouter } from "vue-router";
import { craeteDrug } from "../Api/drugApi";
import { useDrugs } from "../store/drugStore";
import { toasted } from "@/utils/utils";
import { useForm } from "@/new_form_builder/useForm";
import Textarea from "@/components/new_form_elements/Textarea.vue";
import { closeModal } from "@customizer/modal-x";

const props = defineProps({
  drug: {
    type: Object,
  },
  onSubmit: {
    type: Function,
    required: true
  }
});

function onSubmit({ values }) {
  props.onSubmit(values)
}
</script>

<template>
  <Form
      class="grid grid-cols-3 w-full gap-8"
      id="EditDrugForm"
      v-slot="{ submit }"
    >
      <Input
        name="drugName"
        :value="drug?.drugName"
        validation="required"
        label="Drug Name"
        :attributes="{ placeholder: 'Enter Drug Name', type: 'text' }"
      />
      <Input
        name="drugBrandName"
        :value="drug?.brand[0]?.drugBrandName"
        label="Brand Name"
        :attributes="{ placeholder: 'Enter Brand Name', type: 'text' }"
      />
      <Input
        name="drugCode"
        :value="drug?.drugCode"
        label="Drug Code"
        :attributes="{ placeholder: 'Enter Drug Code', type: 'text' }"
      />
      <Input
        name="category"
        :value="drug?.category"
        label="Category"
        :attributes="{ placeholder: 'Enter Category', type: 'text' }"
      />
      <Input
        name="subCategory"
        :value="drug?.subCategory"
        label="Sub-Category"
        :attributes="{ placeholder: 'Enter Sub-Category', type: 'text' }"
      />
      <Input
        name="dosageForm"
        :value="drug?.dosageForm"
        label="Dosage Form"
        :attributes="{ placeholder: 'Enter Dosage Form', type: 'text' }"
      />
      <Textarea
        name="drugDescription"
        :value="drug?.drugDescription"
        label="Description"
        :attributes="{ placeholder: 'Enter Description  ' }"
      />
  
      <Textarea
        name="indications"
        :value="drug?.indications"
        label="Indications"
        :attributes="{ placeholder: 'Enter Indications' }"
      />
      <Textarea
        name="cautions"
        label="Cautions"
        :value="drug?.cautions"
        :attributes="{ placeholder: 'Enter Cautions' }"
      />
      <Textarea
        name="drugInteractions"
        label="Drug Interactions"
        :value="drug?.drugInteractions"
        :attributes="{ placeholder: 'Enter Drug Interactions' }"
      />
      <Textarea
        name="contraIndications"
        label="Contra-Indications"
        :value="drug?.contraIndications"
        :attributes="{ placeholder: 'Enter Contra-Indications' }"
      />
      <Textarea
        name="sideEffects"
        label="Side Effects"
        :value="drug?.sideEffects"
        :attributes="{ placeholder: 'Enter Side Effects' }"
      />
      <Textarea
        name="doseAndAdministration"
        label="Dose and Administration"
        :value="drug?.doseAndAdministration"
        :attributes="{ placeholder: 'Enter Dose and Administration' }"
      />
      <Textarea
        name="storage"
        label="Storage"
        :value="drug?.storage"
        :attributes="{ placeholder: 'Enter Storage Details' }"
      />
  
      <!-- DrugBrandsDto Fields -->
      <Input
        name="drugBrandCode"
        :value="drug?.brand[0]?.drugBrandCode"
        label="Brand Code"
        :attributes="{ placeholder: 'Enter Brand Code', type: 'text' }"
      />
      <div class="w-full flex pt-10 col-span-3 justify-end">
        <div class="flex w-[8rem] justify-end">
          <FormSubmitButton
            @click.prevent="submit(onSubmit)"
            class="col-span-2 font-bold rounded bg-secondary"
            btn-text="Update"
          />
        </div>
      </div>
    </Form>
</template>
