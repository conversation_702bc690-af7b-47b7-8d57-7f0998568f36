<script setup>
import { ref, computed } from "vue";
import {
    Chart as ChartJS,
    LineElement,
    PointElement,
    CategoryScale,
    LinearScale,
    Title,
    Tooltip,
    Legend,
} from "chart.js";
import { Line } from "vue-chartjs";
import { useApiRequest } from "@/composables/useApiRequest";
import { onMounted } from "vue";
import { getMonthlyCompoundingPrescription, getMonthlyPrescription } from "../dashboard/Api/DashboardApi";

// ✅ Register required Chart.js components
ChartJS.register(LineElement, PointElement, CategoryScale, LinearScale, Title, Tooltip, Legend);


const monthlySales = ref({
    labels: [],
    Prescription: [],
});
const commonthlySales = ref({
    labels: [],
    compounding: [],
});

const req = useApiRequest();
const comreq = useApiRequest();


const fetchMonthlySales = () => {
    req.send(() => getMonthlyPrescription(), (res) => {
        if (res.success) {
            console.log("Response:", res.data);
            const labels = Object.keys(res.data);
            console.log(labels);
            const Prescription = Object.values(res.data);
            console.log(Prescription);
            monthlySales.value = { labels, Prescription };
        }

    }
    )
};

const fetchCompoundingMonthlySales = () => {
    comreq.send(() => getMonthlyCompoundingPrescription(), (res) => {
        if (res.success) {
            console.log("Response:", res.data);
            const labels = Object.keys(res.data);
            console.log(labels);
            const compounding = Object.values(res.data);
            console.log(compounding);
            commonthlySales.value = { labels, compounding };
        }

    }
    )
};

onMounted(() => {
    fetchMonthlySales();
    fetchCompoundingMonthlySales();
});

const chartData = computed(() => ({
    labels: monthlySales.value.labels,
    datasets: [
        {
            label: "Compounding Prescriptions",
            data: commonthlySales.value.compounding,
            borderColor: "#FFCF00",
            backgroundColor: "#FFCF00",
            tension: 0.4, // Smooth curve effect
            pointRadius: 0, // Hide points
            borderWidth: 3,
        },
        {
            label: "Paper Prescriptions",
            data: monthlySales.value.Prescription,
            borderColor: "#55291B80",
            backgroundColor: "#55291B80",
            tension: 0.4, //Smooth curve effect
            pointRadius: 0, // Hide points
            borderWidth: 3,
        },
    ],
}));

const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
        x: {
            grid: {
                display: false,
            },
            ticks: {
                color: "#333",
                font: {
                    size: 14,
                },
            },
        },
        y: {
            min: 0,
            max: 50,
            ticks: {
                stepSize: 5,
                color: "#333",
                font: {
                    size: 12,
                },
            },
        },
    },
    plugins: {
        legend: {
            position: "bottom",
            labels: {
                usePointStyle: true,
                pointStyle: "circle",
            },
        },
        annotation: {
            annotations: {
                line1: {
                    type: "line",
                    xMin: 6.5, //Position at July (7th index)
                    xMax: 6.5,
                    borderColor: "#55291B80",
                    borderWidth: 2,
                    borderDash: [4, 4], //  Dashed vertical line
                },
            },
        },
    },
};
</script>

<template>
    <div class="bg-white p-4">
        <div class="h-[300px]">
            <Line :data="chartData" :options="chartOptions" />
        </div>
    </div>
</template>
