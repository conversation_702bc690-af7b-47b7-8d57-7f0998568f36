import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();
const path = "/supplier";

export function getAllSuppliers(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path}/all${qr}`);
}

export function createSupplier(data) {
  return api.addAuthenticationHeader().post(`${path}/register`, data);
}

export function getSupplierById(id) {
  return api.addAuthenticationHeader().get(`${path}/${id}`);
}

export function updateSupplier(id, data) {
  return api.addAuthenticationHeader().put(`${path}/${id}`, data);
}

export function removeSupplier(id) {
  return api.addAuthenticationHeader().delete(`${path}/${id}`);
}
