<script setup>
import Table from '@/components/Table.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { usePagination } from '@/composables/usePagination';
import TableRowSkeleton from '@/skeletons/TableRowSkeleton.vue';
import { getExportSalesReport, getSalesReport } from '../Api/Reportapi';
import { ref, watch } from 'vue';
import { formatCurrency, secondDateFormat } from '@/utils/utils';
import Button from '@/components/Button.vue';
import icons from '@/utils/icons';

const props = defineProps({
	fromDate: String,
	toDate: String,
	paymentType: Array
});

const excel = ref()
const searchQuery = ref('');
const initialLoad = ref(false);
const pagination = usePagination({
	cb: (data) => getSalesReport({
		...data, page: 1, limit: 25, status: "PAID", fromDate: props.fromDate,
		toDate: props.toDate, paymentType: props.paymentType
	})
});

// Only fetch when props change AND we have initial data
watch(
	() => [props.fromDate, props.toDate, props.paymentType],
	() => {
		if (initialLoad.value && props.fromDate && props.toDate) {
			pagination.send();
		}
	},
	{ deep: true }
);
// Initial fetch
if (props.fromDate && props.toDate) {
	pagination.send();
	initialLoad.value = true;
}
const reportReq = useApiRequest()

function genReport() {
	reportReq.send(
		() => getExportSalesReport({ status: "PAID", fromDate: props.fromDate, toDate: props.toDate, paymentType: props.paymentType, export: true }),
		res => {
			if (res.success) {
				const url = URL.createObjectURL(res.data)
				excel.value.href = url
				excel.value.setAttribute('download', `report_from_${secondDateFormat(props.fromDate)}_to_${secondDateFormat(props.toDate)}.xlsx`);
				excel.value.click()
			}
		}
	)
}

</script>

<template>
	<div>
		<div class="flex justify-between items-center mb-4 gap-4">
			<!-- Left side: Report info -->
			<div class="text-sm text-gray-600 flex flex-col gap-4">
				<div class="" v-if="fromDate && toDate">
					Showing report from {{ fromDate }} to {{ toDate }}
				</div>
				<div class="" v-if="paymentType">
					Payment Type: {{ paymentType.join() }}
				</div>
				<div class="font-bold">
					Total Price: {{ formatCurrency(pagination.data.value?.totalAmount) }}
				</div>
			</div>


			<div class="flex items-center gap-4">
				<div
					class="flex items-center gap-4 pl-4 pr-4 py-2 bg-white border rounded-lg focus:primary focus:ring-2 focus:ring-primary">
					<i v-html="icons.search" />

					<input class="placeholder:opacity-65" v-model="pagination.search.value" type="text"
						placeholder="Search Drug Name..." />
				</div>
				<a ref='excel' />
				<Button type="primary" @click="genReport" class="hover:bg-blue-600 transition flex gap-2 items-center"
					:disabled="!pagination.data.value?.prescriptionGetResponses?.length">
					<i class="text-white" v-html="icons.export" />
					Export
				</Button>
			</div>
		</div>

		<Table class="bg-white" :pending="pagination.pending.value" :headers="{
			head: [
				'Cashier Name',
				'Serial Number',
				'Patient Name',
				'Drug Name',
				'Brand Name',
				'Quantity',
				'Price',
			],
			row: [
				'updatedBy',
				'fsNumber',
				'fullname',
				'drugName',
				'drugBrandName',
				'totalQuantity',
				'drugPrice',
			],
		}" :rows="(pagination.data.value?.prescriptionGetResponses || []).reduce((state, pres) => {
			const prescription = pres.drugPrescriptions || []
			prescription.forEach((el) => {
				state.push({
					...el,
					...pres
				})
			})

			return state
		}, [])" :cells="{
			fullname: (_, row) => {
				return `${row?.patient?.firstName} ${row?.patient?.fatherName}`
			},
			totalQuantity: (_, row) => {
				return `${row?.totalQuantity} ${row?.unit}`
			},
			drugPrice: (price) => {
				return formatCurrency(price)
			}
		}">
			<template #actions="{ row }">
			</template>
		</Table>
	</div>
</template>