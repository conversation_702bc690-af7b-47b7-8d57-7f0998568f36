<script setup>
import Button from '@/components/Button.vue';
import Input from '@/components/new_form_elements/Input.vue';
import MultipleSelect from '@/components/new_form_elements/MultipleSelect.vue';
import Select from '@/components/new_form_elements/Select.vue';
import Table from '@/components/Table.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { usePagination } from '@/composables/usePagination';
import Form from '@/new_form_builder/Form.vue';
import TableRowSkeleton from '@/skeletons/TableRowSkeleton.vue';
import icons from '@/utils/icons';
import { Icon } from '@iconify/vue';
import { ref, watch } from 'vue';
import { getReport } from '../../../StoreKeeper/api/InventoryApi';
import SalesReport from '../component/SalesReport.vue';

const components = [
    {
        name: "Daily Sales Report",
        component: SalesReport,
    },

];

const selectedReport = ref(null);
const showReport = ref(false);

const reportParams = ref({
    fromDate: new Date().toISOString().split('T')[0],
    toDate: new Date().toISOString().split('T')[0],
    paymentType: []
});

const search = ref('');
const selectedParameters = ref([]);
const req = useApiRequest()

const reportOptions = ["Daily Sales Report", "", "", " "];
const paymentOptions = ["Cash", "Credit", "Online", "Insurance"]

const isGeneratingReport = ref(false);

function fetchData({ values }) {
    console.log(values);

    if (isGeneratingReport.value) return;

    isGeneratingReport.value = true;
    reportParams.value = {
        fromDate: values.fromDate,
        toDate: values.toDate,
        paymentType: selectedReport.value === "Daily Sales Report" ? values.paymentType : null
    };
    showReport.value = true;

    // selectedReport.value = values.reporttype;

    setTimeout(() => {
        isGeneratingReport.value = false;
    }, 800);
}
console.log(selectedReport.value);
console.log(reportParams.value.fromDate);

// Reset showReport when changing report type
watch(selectedReport, () => {
    showReport.value = false;
});

const getComponent = (name) => {
    const found = components.find(item => item.name === name);
    return found ? found.component : null;
};
const hasComponent = (name) => {
    return components.some(item => item.name === name);
};

function validateDates(fromDate, toDate) {
    if (new Date(toDate) < new Date(fromDate)) {
        alert('End date cannot be before start date');
        return false;
    }
    return true;
}

</script>

<template>
    <div class="h-full w-full flex flex-col bg-white gap-9 px-[124px] mt-7 py-6">

        <Form class="grid grid-cols-5 gap-6 " v-slot="{ submit }" id="reportform">

            <Select class="col-span-2" name="reporttype" label="Search and Select Report Type" validation="required"
                v-model="selectedReport" :options="reportOptions" :attributes="{
                    type: 'text',
                    placeholder: 'Select Report TYpe',
                }">
            </Select>
            <!-- <Select v-if="selectedReport === 'Daily Sales Report'" class="" name="paymentType"
                label="Select Payment Type" v-model="reportParams.paymentType" :options="paymentOptions"
                :attributes="{
                    type: 'text',
                    placeholder: 'Select Payment TYpe',
                }">
            </Select> -->

            <MultipleSelect v-if="selectedReport === 'Daily Sales Report'" name="paymentType"
                label="Select Payment Type" v-model="reportParams.paymentType" :options="paymentOptions" :attributes="{
                    type: 'text',
                    placeholder: 'Select Payment TYpe',
                }" />

            <Input @clcik="validateDates(reportParams.fromDate, reportParams.toDate)" name="fromDate" label="From"
                v-model="reportParams.fromDate" :attributes="{
                    type: 'datetime-local',
                    placeholder: 'Select Date'
                }" />
            <Input @click="validateDates(reportParams.fromDate, reportParams.toDate)" name="toDate" label="To"
                v-model="reportParams.toDate" :attributes="{
                    type: 'datetime-local',
                    placeholder: 'Select Date'
                }" />
            <hr class="col-span-5">

            <Button type="primary" @click.prevent="submit(fetchData)" class=" col-span-5"
                :disabled="isGeneratingReport">
                <div v-if="isGeneratingReport" class="flex justify-center items-center h-full">
                    <Icon icon="svg-spinners:3-dots-scale" class="text-2xl" />
                </div>
                <div v-else class="flex justify-center items-center h-full">
                    Get Report
                </div>
            </Button>
        </Form>


        <div class="bg-[#F1F8F7]  p-4">
            <div class="flex justify-between items-center mb-2">
                <div class="opacity-65 font-bold text-lg">
                    Report Preview
                </div>

            </div>
            <component :is="getComponent(selectedReport)"
                v-if="showReport && selectedReport && hasComponent(selectedReport)" :fromDate="reportParams.fromDate"
                :toDate="reportParams.toDate" :paymentType="reportParams.paymentType">
            </component>
            <div v-else class="p-4 text-center text-gray-500">
                Select a report type and click "Get Report" to view data
            </div>
        </div>
    </div>
</template>