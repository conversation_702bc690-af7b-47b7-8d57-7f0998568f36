import Dashboard from "@/features/new_admin/dashboard/pages/Dashboard.vue";
// import Drugs from "@/features/new_admin/drug/pages/Drugs.vue";
import AddPrivilege from "@/features/new_admin/privilege/pages/AddPrivilege.vue";
import EditPrivilege from "@/features/new_admin/privilege/pages/EditPrivilege.vue";
import Privileges from "@/features/new_admin/privilege/pages/Privileges.vue";
import Report from "@/features/new_admin/AdminReport/pages/Report.vue";
import AddRole from "@/features/new_admin/role/pages/AddRole.vue";
import EditRole from "@/features/new_admin/role/pages/EditRole.vue";
import Role from "@/features/new_admin/role/pages/Role.vue";
import EditUser from "@/features/new_admin/user/pages/EditUser.vue";
import Users from "@/features/new_admin/user/pages/Users.vue";
import { compile } from "vue";
import Branches from "@/features/new_admin/Branches/pages/Branches.vue";
import AddBranches from "@/features/new_admin/Branches/pages/AddBranches.vue";
import Transfer from "@/features/new_admin/Transfers/pages/Transfer.vue";
import Requested from "@/features/new_admin/Purchases/pages/Requested.vue";
import Purchases from "@/features/new_admin/Purchases/pages/Purchases.vue";
import CentralWarehouse from "@/features/new_admin/Central Warehouse/pages/CentralWarehouse.vue";
import DrugLibrary from "@/features/new_admin/Drug Library/pages/DrugLibrary.vue";
import ViewRequested from "@/features/new_admin/Purchases/pages/ViewRequested.vue";
import RequestedIndex from "@/features/new_admin/Purchases/components/RequestedIndex.vue";
import Approval from "@/features/new_admin/Purchases/pages/Approval.vue";
import EditBranch from "@/features/new_admin/Branches/pages/EditBranch.vue";
import InPurchaseProcess from "@/features/new_admin/Purchases/pages/InPurchaseProcess.vue";
import StockTransferMemoPdf from "@/features/new_admin/Branches/components/StockTransferMemoPdf.vue";
import Drugs from "@/features/new_admin/drugs/pages/Drugs.vue";
import Supplier from "@/features/new_admin/supplier/pages/Supplier.vue";
import Manufacturer from "@/features/new_admin/manufacturer/pages/Manufacturer.vue";
import AdminInventory from "@/features/new_admin/admin_inventory/AdminInventory.vue";
import PurchaseRequestDetail from "@/features/new_admin/Purchases/pages/PurchaseRequestDetail.vue";
import Customers from "@/features/customers/pages/Customers.vue";
import Institutions from "@/features/institutions/pages/Institutions.vue";
import WarehouseStockRecieve from "@/features/new_admin/Purchases/pages/WarehouseStockRecieve.vue";
import PurchaseOrderDetail from "@/features/new_admin/Purchases/pages/PurchaseOrderDetail.vue";
import WarehouseStockRecieveDetail from "@/features/new_admin/Purchases/pages/WarehouseStockRecieveDetail.vue";
import WarehouseStockStatus from "@/features/new_admin/Purchases/pages/WarehouseStockStatus.vue";
import Units from "@/features/new_admin/units/pages/Units.vue";


export default [
  {
    path: "dashboard",
    name: "Admin Dashboard",
    component: Dashboard,
    // role: "Admin",
    meta: {
      requiresAuth: true,
      privileges: ["view_dashboard"],
    },
  },
  {
    path: "role",
    name: "admin role",
    component: Role,
    meta: {
      requiresAuth: true,
      // role: "Admin",
      privileges: ["view_role"],
    },
  },
  {
    path: "create-role",
    name: "creatrole",
    component: AddRole,
    meta: {
      requiresAuth: true,
      // role: "Admin",
      privileges: ["view_role"],
    },
  },
  {
    path: "privileges",
    name: "Privilege",
    component: Privileges,
    meta: {
      requiresAuth: true,
      // role: "Admin",
      privileges: ["view_privilege"],
    },
  },
  {
    path: "edit_privilege/:privilegeUuid",
    name: "Edit Privilege",
    component: EditPrivilege,
    meta: {
      requiresAuth: true,
      // role: "Admin",
      privileges: ["view_privilege"],
    },
  },
  {
    path: "/drugs",
    name: "drug",
    component: Drugs,
    meta: {
      requiresAuth: true,
      // role: "admin",
      privileges: ["view_drugs"],
    },
  },
  {
    path: "/suppliers",
    name: "Admin Suppliers",
    component: Supplier,
    meta: {
      requiresAuth: true,
      privileges: ["view_suppliers"],
    },
  },
  {
    path: "/manufacturers",
    name: "Manufacturers",
    component: Manufacturer,
    meta: {
      requiresAuth: true,
      privileges: ["view_manufacturers"],
    },
  },
  {
    path: "/admin/inventory",
    component: AdminInventory,
    name: "Admin Inventory",
    meta: {
      requiresAuth: true,
      privileges: ["view_admin_inventory"],
    },
  },
  {
    path: "create-privilege",
    name: "creatprivilege",
    component: AddPrivilege,
    meta: {
      requiresAuth: true,
      // role: "Admin",
      privileges: ["view_privilege"],
    },
  },
  {
    path: "user",
    name: "admin user",
    component: Users,
    meta: {
      requiresAuth: true,
      // role: "Admin",
      privileges: ["view_user"],
    },
  },
  {
    path: "edit_user/:userUuid",
    name: "Edit User",
    component: EditUser,
    meta: {
      requiresAuth: true,
      // role: "Admin",
      privileges: ["view_user"],
    },
  },
  // {
  //   path: "create-role",
  //   name: "createrole",
  //   component: AddRole,
  //   meta: {
  //     requiresAuth: true,
  //     // role: "Admin",
  //     privileges: ["view_role"],
  //   },
  // },
  {
    path: "edit_role/:roleUuid",
    name: "edit role",
    component: EditRole,
    meta: {
      requiresAuth: true,
      // role: "admin",
      privileges: ["view_role"],
    },
  },
  // {
  //   path: "drugs",
  //   name: "drug",
  //   component: Drugs,
  //   meta: {
  //     requiresAuth: true,
  //     // role: "admin",
  //     privileges: ["view_drugs"],
  //   },
  // },
  {
    path: "drugslibrary",
    name: "Drugs Library",
    component: DrugLibrary,
    meta: {
      requiresAuth: true,
      // role: "admin",
      privileges: ["view_drugs"],
    },
  },
  {
    path: "report",
    name: "Report",
    component: Report,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "branches",
    name: "Branches",
    component: Branches,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "units",
    name: "Units",
    component: Units,
    meta: {
      requiresAuth: true,
      privileges: ["view_unit"],
    },
  },
  {
    path: "addbranch",
    name: "Add Branch",
    component: AddBranches,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "edit_branch/:branchUuid",
    name: "Edit Branch",
    component: EditBranch,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "transfers",
    name: "Transfers",
    component: Transfer,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "purchaserequests",
    component: RequestedIndex,
    meta: {
      privileges: ["view_purchaserequest"],
    },
    children: [
      {
        path: "",
        name: "Purchase Requests",
        component: Requested,
        meta: {
          requiresAuth: true,
          privileges: ["view_purchaserequest"],
        },
      },
      {
        path: "purchaserequest-detail",
        name: "Purchase Request Detail",
        component: PurchaseRequestDetail,
        meta: {
          requiresAuth: true,
          privileges: ["view_purchaserequest"],
        },
      },
    ],
  },
  {
    path: "requestapproval",
    name: "Purchase Requests Approval",
    component: Approval,
    meta: {
      requiresAuth: true,
      privileges: ["view_dashboard"],
    },
  },
  {
    path: "/warehouse-stock-recieve",
    name: "Warehouse Stock Recieve",
    component: WarehouseStockRecieve,
    meta: {
      requiresAuth: true,
      privileges: ["view_purchaserequest"],
    },
  },
  {
    path: "/warehouse-stock-status",
    name: "Warehouse Stock Status",
    component: WarehouseStockStatus,
    meta: {
      requiresAuth: true,
      privileges: ["view_purchaserequest"],
    },
  },
  {
    path: "inpurchaseprocess",
    name: "Purchase Order",
    component: InPurchaseProcess,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "purchase-order-detail/:orderId",
    name: "Purchase Order Detail",
    component: PurchaseOrderDetail,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "stock-recieve-detail/:orderId",
    name: "Stock Recieve Detail",
    component: WarehouseStockRecieveDetail,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "/customer-list",
    name: "Customer List",
    component: Customers,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "Instituions",
    name: "Instituions",
    component: Institutions,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "purchases",
    name: "Purchases",
    component: Purchases,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "centralwarehouse",
    name: "Central Warehouse",
    component: CentralWarehouse,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
  {
    path: "stocktransfermemo",
    name: "Stock Transfer Memo",
    component: StockTransferMemoPdf,
    meta: {
      requiresAuth: true,
      privileges: ["view_report"],
    },
  },
];
