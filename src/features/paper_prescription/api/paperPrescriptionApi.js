import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();
const addPrescriptionUrl = new ApiService(import.meta.env?.v_API_UR);

const a = new ApiService();

const path = "/stock/inventory";
const path2 = "/stock/store";

export function finalizeKotera(data) {
  return api.addAuthenticationHeader().put(`${path}/finalize-kotera`, data);
}


export function finalizStoreKotera(data) {
  return api.addAuthenticationHeader().put(`${path2}/finalize-kotera`, data);
}

export function checkMedicineAvailability(query = {}, config) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path}/pharmacyInventory${qr}`);
}

export function getNextBatch(id) {
  return api
    .addAuthenticationHeader()
    .get(`${path}/check_second_to_expire_drug`, {
      params: {
        search: id,
      },
    });
}

export function checkDrugAvailability(query = {}, config) {
  return api.addAuthenticationHeader().get(`${path}/getAvailableDrugs`, {
    ...config,
    params: query,
  });
}

export function checkAvailability(id, query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path}/inventory/${id}`);
}

export function addPrescription(data, config = {}) {
  return addPrescriptionUrl
    .addAuthenticationHeader()
    .post(`prescriptions/addPrescription`, data);
}

export function changePrescription(id, data) {
  return a
    .addAuthenticationHeader()
    .put(`/prescriptions/updatePrescription/${id}`, data);
}

export function getPrescriptionByUuid(data, config = {}) {
  return addPrescriptionUrl
    .addAuthenticationHeader()
    .get(`prescriptions/${data}`);
}

export function updatePrescription(query = {}, data = {}, config = {}) {
  return addPrescriptionUrl
    .addAuthenticationHeader()
    .put(`prescriptions/updatePrescriptionStatus`, data, {
      ...config,
      params: query,
    });
}

export function changePrescriptionBystatus(query = {}, data) {
  const qr = getQueryFormObject(query);
  return a
    .addAuthenticationHeader()
    .put(`/prescriptions/updatePrescriptionStatus${qr}`, data);
}

export function getStore(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path2}/all${qr}`);
}
