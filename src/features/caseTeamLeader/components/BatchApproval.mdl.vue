<script setup>
import Button from "@/components/Button.vue";
import NewFormParent from "@/features/new_admin/role/components/NewFormParent.vue";
import Form from "@/new_form_builder/Form.vue";
import icons from "@/utils/icons";
import { BatchStatus, toasted } from "@/utils/utils";
import { useApiRequest } from "@/composables/useApiRequest";
import { closeModal } from "@customizer/modal-x";
import { ref } from "vue";
// import { approveBatch, rejectBatch } from "../api/batchApprovalApi";
import { useBatchApprovalStore } from "../store/batchApprovalStore";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const batchApprovalStore = useBatchApprovalStore();
const drugs = ref(
  [...props.data?.drugs].map((el) => {
    return {
      ...el,
      approvedAmount: el.approvedAmount || (el.totalAmount + (0 + 0) - el.quantity) * 2 - el.quantity,
      remark: el.remark || "",
    };
  })
);

const approveReq = useApiRequest();
const rejectReq = useApiRequest();
const rejectionReason = ref("");
const showRejectionForm = ref(false);

function approveBatchRequest() {
  if(approveReq.pending.value) return;

  approveReq.send(
    () => approveBatch(props.data?.batchUuid, drugs.value?.map(el => ({
      "approvedAmount": el.approvedAmount,
      "toInventoryUuid": el.inventoryUuid,
      "remark": el.remark
    }))),
    res => {
      if(res.success) {
        batchApprovalStore.updateBatchStatus(props.data?.batchUuid, BatchStatus.APPROVED);
        toasted(true, 'Batch approved successfully', '');
        closeModal();
      } else {
        toasted(false, '', res.error || 'Failed to approve batch');
      }
    }
  );
}

function rejectBatchRequest() {
  if(rejectReq.pending.value || !rejectionReason.value) return;

  rejectReq.send(
    () => rejectBatch(props.data?.batchUuid, rejectionReason.value),
    res => {
      if(res.success) {
        batchApprovalStore.updateBatchStatus(props.data?.batchUuid, BatchStatus.REJECTED, { rejectionReason: rejectionReason.value });
        toasted(true, 'Batch rejected successfully', '');
        closeModal();
      } else {
        toasted(false, '', res.error || 'Failed to reject batch');
      }
    }
  );
}
</script>

<template>
  <div
    @click.stop="() => {}"
    class="bg-black/50 h-55 p-10 min-h-full w-full grid place-items-center"
  >
    <NewFormParent size="xl" title="Batch Approval">
      <template #left-actions>
        <i class="text-black" v-html="icons.leftArrow" />
      </template>
      <Form v-slot="{ submit }" id="batchApproval" class="flex flex-col gap-4">
        <div class="flex items-center justify-between">
          <p>Batch Request Details</p>
          <div class="flex gap-2">
            <Button
              v-if="!showRejectionForm"
              @click="showRejectionForm = true"
              type="edge"
            >
              Reject
            </Button>
            <Button
              v-if="!showRejectionForm"
              :pending="approveReq.pending.value"
              @click.prevent="submit(approveBatchRequest)"
              type="primary"
            >
              Approve
            </Button>
          </div>
        </div>

        <div v-if="showRejectionForm" class="border p-4 rounded-md mb-4">
          <h3 class="font-semibold mb-2">Rejection Reason</h3>
          <textarea
            v-model="rejectionReason"
            class="w-full border rounded-md p-2 mb-2"
            rows="3"
            placeholder="Enter reason for rejection"
          ></textarea>
          <div class="flex justify-end gap-2">
            <Button @click="showRejectionForm = false" type="edge">Cancel</Button>
            <Button
              :pending="rejectReq.pending.value"
              :disabled="!rejectionReason"
              @click="rejectBatchRequest"
              type="secondary"
            >
              Confirm Rejection
            </Button>
          </div>
        </div>

        <table class="border rounded-md" v-if="!showRejectionForm">
          <thead>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th class="max-w-12" rowspan="3">Ser. No.</th>
              <th rowspan="3">Item</th>
              <th rowspan="3">Unit</th>
              <th colspan="7">Completed By Unit</th>
              <th colspan="3">Completed By Store</th>
              <th rowspan="2">Qty to be Supplied</th>
              <th rowspan="2">Remark</th>
            </tr>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th colspan="4">Beginning Balance</th>
              <th>Quantity Received</th>
              <th>Loss/Adjustment</th>
              <th>Ending Balance</th>
              <th>Calculated Consumption E=A+B+-C-D</th>
              <th>Max F=E*2</th>
              <th>Qty to Max G=F-D</th>
            </tr>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th colspan="4">A</th>
              <th>B</th>
              <th>C</th>
              <th>D</th>
              <th>E</th>
              <th>F</th>
              <th>G</th>
              <th>H</th>
              <th>I</th>
            </tr>
          </thead>
          <tbody>
            <template v-if="drugs.length > 0">
              <tr
                class="*:text-center *:border text-sm *:p-1"
                v-for="(item, idx) in drugs || []"
                :key="item.drugName"
              >
                <td class="max-w-12">{{ idx + 1 }}</td>
                <td>{{ item.drugName }}</td>
                <td>{{ item?.dosageForm }}</td>
                <td colspan="4">
                  {{ item.totalAmount }}
                </td>
                <td>0</td>
                <td>0</td>
                <td>
                  {{ item.quantity }}
                </td>
                <td>{{ item.totalAmount + (0 + 0) - item.quantity }}</td>
                <td>
                  {{ (item.totalAmount + (0 + 0) - item.quantity) * 2 }}
                </td>
                <td>
                  {{
                    (item.totalAmount + (0 + 0) - item.quantity) * 2 -
                    item.quantity
                  }}
                </td>
                <td>
                  <input
                    class="max-w-max w-12 text-center"
                    v-model="item.approvedAmount"
                  />
                </td>
                <td>
                  <input
                    v-model="item.remark"
                    class="max-w-max w-12 text-center"
                    placeholder="remark"
                  />
                </td>
              </tr>
            </template>
            <tr v-else>
              <td colspan="17" class="p-4">
                <div class="flex flex-col items-center justify-center">
                  <i class="*:size-56" v-html="icons.no_data" />
                  No Data
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </Form>
    </NewFormParent>
  </div>
</template>
