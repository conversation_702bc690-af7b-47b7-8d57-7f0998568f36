<script setup>
import <PERSON>ton from "@/components/Button.vue";
import NewFormParent from "@/features/new_admin/role/components/NewFormParent.vue";
import Form from "@/new_form_builder/Form.vue";
import icons from "@/utils/icons";
import { BatchStatus } from "@/utils/utils";
import { closeModal } from "@customizer/modal-x";
import { ref, computed } from "vue";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const drugs = ref(props.data?.drugs || []);

const statusClass = computed(() => {
  if (props.data?.status === BatchStatus.APPROVED) {
    return 'bg-green-100 text-green-800';
  } else if (props.data?.status === BatchStatus.REJECTED) {
    return 'bg-red-100 text-red-800';
  }
  return '';
});
</script>

<template>
  <div
    @click.stop="() => {}"
    class="bg-black/50 h-55 p-10 min-h-full w-full grid place-items-center"
  >
    <NewFormParent size="xl" title="Batch Details">
      <template #left-actions>
        <i class="text-black" v-html="icons.leftArrow" />
      </template>
      <div class="flex flex-col gap-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <h2 class="text-lg font-semibold">Batch Status:</h2>
            <span :class="statusClass" class="px-3 py-1 rounded-full text-sm font-medium">
              {{ data?.status }}
            </span>
          </div>
          <Button @click="closeModal" type="edge">Close</Button>
        </div>

        <div v-if="data?.status === BatchStatus.REJECTED" class="border p-4 rounded-md mb-4 bg-red-50">
          <h3 class="font-semibold mb-2">Rejection Reason:</h3>
          <p>{{ data?.rejectionReason || 'No reason provided' }}</p>
        </div>

        <table class="border rounded-md">
          <thead>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th class="max-w-12" rowspan="3">Ser. No.</th>
              <th rowspan="3">Item</th>
              <th rowspan="3">Unit</th>
              <th colspan="7">Completed By Unit</th>
              <th colspan="3">Completed By Store</th>
              <th rowspan="2">Approved Qty</th>
              <th rowspan="2">Remark</th>
            </tr>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th colspan="4">Beginning Balance</th>
              <th>Quantity Received</th>
              <th>Loss/Adjustment</th>
              <th>Ending Balance</th>
              <th>Calculated Consumption E=A+B+-C-D</th>
              <th>Max F=E*2</th>
              <th>Qty to Max G=F-D</th>
            </tr>
            <tr class="*:border text-sm *:p-1 font-bold">
              <th colspan="4">A</th>
              <th>B</th>
              <th>C</th>
              <th>D</th>
              <th>E</th>
              <th>F</th>
              <th>G</th>
              <th>H</th>
              <th>I</th>
            </tr>
          </thead>
          <tbody>
            <template v-if="drugs.length > 0">
              <tr
                class="*:text-center *:border text-sm *:p-1"
                v-for="(item, idx) in drugs || []"
                :key="item.drugName"
              >
                <td class="max-w-12">{{ idx + 1 }}</td>
                <td>{{ item.drugName }}</td>
                <td>{{ item?.dosageForm }}</td>
                <td colspan="4">
                  {{ item.totalAmount }}
                </td>
                <td>0</td>
                <td>0</td>
                <td>
                  {{ item.quantity }}
                </td>
                <td>{{ item.totalAmount + (0 + 0) - item.quantity }}</td>
                <td>
                  {{ (item.totalAmount + (0 + 0) - item.quantity) * 2 }}
                </td>
                <td>
                  {{
                    (item.totalAmount + (0 + 0) - item.quantity) * 2 -
                    item.quantity
                  }}
                </td>
                <td>
                  {{ item.approvedAmount }}
                </td>
                <td>
                  {{ item.remark || '-' }}
                </td>
              </tr>
            </template>
            <tr v-else>
              <td colspan="17" class="p-4">
                <div class="flex flex-col items-center justify-center">
                  <i class="*:size-56" v-html="icons.no_data" />
                  No Data
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </NewFormParent>
  </div>
</template>
