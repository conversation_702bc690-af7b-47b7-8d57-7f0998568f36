import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();
const path = "/manufacturer";

export function getAllManufacturers(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path}/all${qr}`);
}

export function createManufacturer(data) {
  return api.addAuthenticationHeader().post(`${path}/register`, data);
}

export function getManufacturerById(id) {
  return api.addAuthenticationHeader().get(`${path}/${id}`);
}

export function updateManufacturer(id, data) {
  return api.addAuthenticationHeader().put(`${path}/${id}`, data);
}

export function removeManufacturer(id) {
  return api.addAuthenticationHeader().delete(`${path}/${id}`);
}
