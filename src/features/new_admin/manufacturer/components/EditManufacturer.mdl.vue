<script setup>
import { useApiRequest } from '@/composables/useApiRequest';
import { toasted } from '@/utils/utils';
import { closeModal } from '@customizer/modal-x';
import { updateManufacturer } from '../api/ManufacturerApi';
import { useManufacturers } from '../store/manufacturerStore';
import ManufacturerForm from '../form/ManufacturerForm.vue';
import NewFormParent from '@/features/admin/role/components/NewFormParent.vue';
import Button from '@/components/Button.vue';
import { useForm } from '@/new_form_builder/useForm';

const props = defineProps({
  data: Object
});

const manufacturerStore = useManufacturers();
const req = useApiRequest();

function editManufacturer({ values }) {
  req.send(
    () => updateManufacturer(props.data.manufacturerUuid, values),
    (res) => {
      if (res.success) {
        manufacturerStore.update(props.data.manufacturerUuid, { ...props.data, ...values });
        closeModal();
      }
      toasted(res.success, 'Manufacturer updated successfully', res.error);
    }
  );
}

const {submit} = useForm('manufacturerForm')
</script>

<template>
  <div class="grid place-items-center p-4 bg-black/50 min-h-full" >
    <NewFormParent title="Edit Manufacturer">
      <ManufacturerForm :manufacturer="data" />
      <template #bottom >
        <div class="gap-4 p-2 grid grid-cols-3">
          <Button @click="closeModal" type="edge">Cancel</Button>
          <Button :pending="req.pending.value" @click.prevent="submit(editManufacturer)" class="cols-span-2" type="secondary" form="manufacturerForm">Update</Button>
        </div>
      </template>
    </NewFormParent>
  </div>
</template>
