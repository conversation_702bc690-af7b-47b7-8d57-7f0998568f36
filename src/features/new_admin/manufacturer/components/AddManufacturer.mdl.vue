<script setup>
import { useApiRequest } from '@/composables/useApiRequest';
import { toasted } from '@/utils/utils';
import { closeModal } from '@customizer/modal-x';
import { createManufacturer } from '../api/ManufacturerApi';
import { useManufacturers } from '../store/manufacturerStore';
import ManufacturerForm from '../form/ManufacturerForm.vue';
import NewFormParent from '@/features/admin/role/components/NewFormParent.vue';
import Button from '@/components/Button.vue';
import { useForm } from '@/new_form_builder/useForm';

const manufacturerStore = useManufacturers();
const req = useApiRequest();

function addManufacturer({ values }) {
  req.send(
    () => createManufacturer(values),
    (res) => {
      if (res.success) {
        manufacturerStore.add(res.data);
        closeModal();
      }
      toasted(res.success, 'Manufacturer added successfully', res.error);
    }
  );
}

const {submit} = useForm('manufacturerForm')
</script>

<template>
  <div class="grid place-items-center p-4 bg-black/50 min-h-full" >
    <NewFormParent title="Add New Manufacturer">
      <ManufacturerForm />
      <template #bottom >
        <div class="gap-4 p-2 grid grid-cols-3">
          <Button @click="closeModal" type="edge">Cancel</Button>
          <Button :pending="req.pending.value" @click.prevent="submit(addManufacturer)" class="col-span-2" type="secondary" form="manufacturerForm">Save</Button>
        </div>
      </template>
    </NewFormParent>
  </div>
</template>
