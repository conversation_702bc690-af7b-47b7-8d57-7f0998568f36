<script setup>
import Input from '@/components/new_form_elements/Input.vue';
import Form from '@/new_form_builder/Form.vue';
import { useForm } from '@/new_form_builder/useForm';

defineProps({
  manufacturer: {
    type: Object,
    default: () => ({})
  }
});

</script>

<template>
  <Form :inner="false" id="manufacturerForm" class="gap-4 grid grid-cols-2">
    <Input 
      name="manufacturerName" 
      label="Manufacturer Name" 
      :value="manufacturer?.manufacturerName"
      validation="required" 
      :attributes="{
        placeholder: 'Enter manufacturer name'
      }"
    />
    <Input 
      name="contactPerson" 
      label="Contact Person" 
      :value="manufacturer?.contactPerson"
      :attributes="{
        placeholder: 'Enter contact person name'
      }"
    />
    <Input 
      name="phone" 
      label="Phone Number" 
      :value="manufacturer?.phone"
      validation="required" 
      :attributes="{
        placeholder: 'Enter phone number'
      }"
    />
    <Input 
      name="email" 
      label="Email" 
      :value="manufacturer?.email"
      validation="email"
      :attributes="{
        placeholder: 'Enter email address'
      }"
    />
    <Input 
      name="address" 
      label="Address" 
      :value="manufacturer?.address"
      validation="required" 
      :attributes="{
        placeholder: 'Enter address' 
      }"
    />
    <Input 
      name="country" 
      label="Country" 
      :value="manufacturer?.country"
      validation="required" 
      :attributes="{
        placeholder: 'Enter country' 
      }"
    />
    <slot></slot>
  </Form>
</template>
