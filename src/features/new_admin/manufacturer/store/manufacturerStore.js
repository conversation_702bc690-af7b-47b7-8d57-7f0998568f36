import { ref } from "vue";
import { defineStore } from "pinia";

export const useManufacturers = defineStore("manufacturerStore", () => {
  const manufacturers = ref([]);

  function set(data) {
    manufacturers.value = data;
  }

  function getAll() {
    return manufacturers.value;
  }
  
  function add(data) {
    manufacturers.value.push(data);
  }

  function update(id, data) {
    const idx = manufacturers.value.findIndex((el) => el.manufacturerUuid == id);
    if (idx == -1) return;

    manufacturers.value.splice(idx, 1, data);
  }
  
  function remove(id) {
    const idx = manufacturers.value.findIndex((el) => el.manufacturerUuid == id);
    if (idx == -1) return;
    
    manufacturers.value.splice(idx, 1);
  }

  return {
    manufacturers,
    getAll,
    update,
    remove,
    set,
    add,
  };
});
