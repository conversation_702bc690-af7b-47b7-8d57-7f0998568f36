<script setup>
import Input from '@/components/new_form_elements/Input.vue';
import Form from '@/new_form_builder/Form.vue';
import { useForm } from '@/new_form_builder/useForm';

defineProps({
  supplier: {
    type: Object,
    default: () => ({})
  }
});

</script>

<template>
  <Form :inner="false" id="supplierForm" class="gap-4 grid grid-cols-2">
    <Input 
      name="supplierName" 
      label="Supplier Name" 
      :value="supplier?.supplierName"
      validation="required" 
      :attributes="{
        placeholder: 'Enter supplier name'

      }"
    />
    <Input 
      name="contactPerson" 
      label="Contact Person" 
      :value="supplier?.contactPerson"
      :attributes="{
        placeholder: 'Enter contact person name'

      }"
    />
    <Input 
      name="phone" 
      label="Phone Number" 
      :value="supplier?.phone"
      validation="required|phone" 
      :attributes="{
        placeholder: 'Enter phone number'

      }"
    />
    <Input 
      name="email" 
      label="Email" 
      :value="supplier?.email"
      validation="email"
      :attributes="{
        placeholder: 'Enter email address'
      }"
    />
    <Input 
      name="address" 
      label="Address" 
      :value="supplier?.address"
      validation="required" 
      :attributes="{
        placeholder: 'Enter address' 
      }"
    />
    <Input 
      name="tinNumber" 
      label="TIN Number" 
      placeholder="Enter TIN number" 
      :value="supplier?.tinNumber"
      :attributes="{
        placeholder: 'TIN Number' 
      }"
    />
    <slot></slot>
  </Form>
</template>
