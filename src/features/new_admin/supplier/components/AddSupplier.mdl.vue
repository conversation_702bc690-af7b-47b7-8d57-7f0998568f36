<script setup>
import { useApiRequest } from '@/composables/useApiRequest';
import { toasted } from '@/utils/utils';
import { closeModal } from '@customizer/modal-x';
import { createSupplier } from '../api/SupplierApi';
import { useSuppliers } from '../store/supplierStore';
import SupplierForm from '../form/SupplierForm.vue';
import NewFormParent from '@/features/new_admin/role/components/NewFormParent.vue';
import Button from '@/components/Button.vue';
import { useForm } from '@/new_form_builder/useForm';

const supplierStore = useSuppliers();
const req = useApiRequest();

function addSupplier({ values }) {
  req.send(
    () => createSupplier(values),
    (res) => {
      if (res.success) {
        supplierStore.add(res.data);
        closeModal();
      }
      toasted(res.success, 'Supplier added successfully', res.error);
    }
  );
}

const {submit} = useForm('supplierForm')
</script>

<template>
  <div class="grid place-items-center p-4 bg-black/50 min-h-full" >
    <NewFormParent title="Add New Supplier">
      <SupplierForm />
      <template #bottom >
        <div class="gap-4 p-2 grid grid-cols-3">
          <Button @click="closeModal" type="edge">Cancel</Button>
          <Button :pending="req.pending.value" @click.prevent="submit(addSupplier)" class="col-span-2" type="secondary" form="supplierForm">Save</Button>
        </div>
      </template>
    </NewFormParent>
  </div>
</template>
