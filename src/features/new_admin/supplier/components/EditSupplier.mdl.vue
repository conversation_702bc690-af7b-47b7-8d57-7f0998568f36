<script setup>
import { useApiRequest } from "@/composables/useApiRequest";
import { toasted } from "@/utils/utils";
import { closeModal } from "@customizer/modal-x";
import { updateSupplier } from "../api/SupplierApi";
import { useSuppliers } from "../store/supplierStore";
import SupplierForm from "../form/SupplierForm.vue";
import NewFormParent from "@/features/admin/role/components/NewFormParent.vue";
import Button from "@/components/Button.vue";
import { useForm } from "@/new_form_builder/useForm";

const props = defineProps({
  data: Object,
});

const supplierStore = useSuppliers();
const req = useApiRequest();

function editSupplier({ values }) {
  req.send(
    () => updateSupplier(props.data.supplierUuid, values),
    (res) => {
      if (res.success) {
        supplierStore.update(props.data.supplierUuid, {
          ...props.data,
          ...values,
        });
        closeModal();
      }
      toasted(res.success, "Supplier updated successfully", res.error);
    }
  );
}

const { submit } = useForm('supplierForm')
</script>

<template>
  <div class="grid place-items-center p-4 bg-black/50 min-h-full">
    <NewFormParent title="Edit Supplier">
      <SupplierForm :supplier="data"> </SupplierForm>
      <template #bottom>
        <div class="gap-4 p-2 grid grid-cols-3">
          <Button @click="closeModal" type="edge">Cancel</Button>
          <Button @click.prevent="submit(editSupplier)" class="col-span-2" type="secondary" form="supplierForm">Update</Button>
        </div>
      </template>
    </NewFormParent>
  </div>
</template>
