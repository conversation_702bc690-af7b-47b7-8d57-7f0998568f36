import { ref } from "vue";
import { defineStore } from "pinia";

export const useSuppliers = defineStore("supplierStore", () => {
  const suppliers = ref([]);

  function set(data) {
    suppliers.value = data;
  }

  function getAll() {
    return suppliers.value;
  }
  
  function add(data) {
    suppliers.value.push(data);
  }

  function update(id, data) {
    const idx = suppliers.value.findIndex((el) => el.supplierUuid == id);
    if (idx == -1) return;

    suppliers.value.splice(idx, 1, data);
  }
  
  function remove(id) {
    const idx = suppliers.value.findIndex((el) => el.supplierUuid == id);
    if (idx == -1) return;
    
    suppliers.value.splice(idx, 1);
  }

  return {
    suppliers,
    getAll,
    update,
    remove,
    set,
    add,
  };
});
