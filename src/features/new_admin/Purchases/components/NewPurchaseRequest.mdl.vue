<script setup>
import Form from "@/new_form_builder/Form.vue";
import NewFormParent from "../../role/components/NewFormParent.vue";
import Input from "@/components/new_form_elements/Input.vue";
import Table from "@/components/Table.vue";
import { usePagination } from "@/composables/usePagination";
import { computed, ref, watch } from "vue";
import { useRouter } from "vue-router";
import PurchaseTableRow from "./PurchaseTableRow.vue";
import Table2 from "@/components/Table2.vue";
import Button from "@/components/Button.vue";
import Textarea from "@/components/new_form_elements/Textarea.vue";
import Select from "@/components/new_form_elements/Select.vue";
import NewPurchaseTableRow from "./NewPurchaseTableRow.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { getAllDrug } from "@/features/new_admin/drugs/Api/drugApi.js";
import { allRequest, toasted } from "@/utils/utils";
import MultipleSelect from "@/components/new_form_elements/MultipleSelect.vue";
import { createRequest } from "../Api/PurchaseRequestApi";
import { useAuth } from "@/stores/auth";
import { closeModal } from "@customizer/modal-x";

const auth = useAuth();
const search = ref("");
const selectedDrug = ref(null);
const drugreq = useApiRequest();
const drugResponse = ref([]);

const tableItems = ref({
  measure: "",
  quantity: 1,
  branch: "",
});

const note = ref("");
const request = useApiRequest();

drugreq.send(
  () => getAllDrug({ page: 1, limit: 500 }),
  (res) => {
    if (res.success) {
      drugResponse.value = res.data.content || [];
      console.log(drugResponse.value);
    }
  }
);

const brandOptions = computed(() => {
  return drugResponse.value?.flatMap?.((drug) =>
    drug.brand.map((brand) => ({
      label: brand.drugBrandName,
      value: brand.brandUuid,
    }))
  ) || [];
});

const drugOptions = computed(() => {
  return drugResponse.value?.map?.((drug) => ({
    label: drug.drugName,
    value: drug.drugUuid,
  })) || [];
});

const tableData = computed(() => {
  if (!selectedDrug.value) return [];

  const selectedDrugObject = drugResponse.value?.find?.(
    (drug) => drug.drugUuid === selectedDrug.value
  );

  console.log("Found drug object:", selectedDrugObject);

  // Return the formatted data for the table
  return [
    {
      drugDetail: selectedDrugObject?.drugName || "N/A",
      unit: selectedDrugObject?.unit || "N/A",
      quantity: 1,
      branch: "",
      drugUuid: selectedDrugObject?.drugUuid,
    },
  ];
});

function submitPurchaseRequest() {
  if (!selectedDrug.value) {
    toasted(false, "", "Please select a drug first");
    return;
  }

  const purchaseData = {
    drugUuid: selectedDrug.value,
    measure: tableItems.value.measure,
    quantity: tableItems.value.quantity,
    note: note.value,
    branch: tableItems.value.branch,
  };

  const requestData = {
    branchUuid: tableItems.value.branch || "main-branch-uuid",
    managerUuid: auth.auth?.user?.userUuid,
    items: [
      {
        drugUuid: selectedDrug.value,
        measure: tableItems.value.measure,
        quantity: tableItems.value.quantity,
        notes: note.value,
      },
    ],
  };

  console.log("Submitting purchase request:", purchaseData);

  request.send(
    () => createRequest(requestData),
    (res) => {
      toasted(
        res.success,
        "Purchase request submitted successfully",
        res.error
      );
      if (res.success) {
        selectedDrug.value = null;
        note.value = "";
      }
    }
  );
}

watch(
  selectedDrug,
  (newVal) => {
    console.log("Selected drug changed:", newVal);
    console.log("Table data:", tableData.value);
  },
  { deep: true }
);
</script>

<template>
  <div
    @click.self="closeModal()"
    class="bg-black/50 h-55 p-10 min-h-full w-full grid place-items-center"
  >
    <NewFormParent size="xs" title="Creating Purchase Request">
      <Form id="purchaseRequestForm" class="">
        <div class="flex flex-col gap-6">
          <Select
            :obj="true"
            v-model="selectedDrug"
            name="brandName"
            label="Brand Name"
            :Arguments="{ placeholder: 'Search and select item' }"
            :options="drugOptions"
          />
          <p class="font-normal text-sm opacity-80">Added Drugs</p>
          <div class="flex-1">
            <Table2
              :lastCol="true"
              v-model="tableItems"
              :headers="{
                head: ['Drug Detail', 'Unit', 'Quantity', 'Branch'],
                row: ['drugDetail', 'unit', 'quantity', 'branch'],
              }"
              :rows="tableData || []"
              :rowCom="NewPurchaseTableRow"
              :cells="{}"
            >
              <!-- <template #lastCol="{ row }">
							<Button @click="removeRow(row)" class="text-sm text-white py-[6px] px-3 rounded-sm"
								type="secondary">Remove</Button>
						</template> -->
            </Table2>
          </div>
          <div>
            <Textarea
              v-model="note"
              name="notes"
              label="Remark"
              :attributes="{ placeholder: 'Chief\'s Complaint' }"
            ></Textarea>
          </div>
          <div class="flex justify-end">
            <button
              @click="submitPurchaseRequest"
              class="bg-primary text-white py-2 px-[34px] border-[0.38px] rounded-[4px] flex gap-1 items-center"
            >
              Request Purchase
            </button>
          </div>
        </div>
      </Form>
    </NewFormParent>
  </div>
</template>
