<script setup>
import Input from "@/components/new_form_elements/Input.vue";
import Textarea from "@/components/new_form_elements/Textarea.vue";
import SearchSelect from "@/components/SearchSelect.vue";
import NewFormParent from "../../role/components/NewFormParent.vue";
import ModalParent from "@/components/ModalParent.vue";
import Button from "@/components/Button.vue";
import Form from "@/new_form_builder/Form.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { closeModal, openModal } from "@customizer/modal-x";
import { toasted } from "@/utils/utils";
import { ref, computed } from "vue";
import { useForm } from "@/new_form_builder/useForm";
import { getAllBranches } from "../../Branches/Api/BranchesApi";
import { updatePurchaseOrderItem } from "../Api/purchaseOrderItemApi";
import SearchSelectInput from "@/components/SearchSelectInput.vue";
import { getDistributionByBrancdId, orderDistribution, updateDistribution } from "../Api/purchaseOrderApi";
import Select from "@/components/new_form_elements/Select.vue";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const getAssignedQuantity = useApiRequest()

getAssignedQuantity.send(() => getDistributionByBrancdId(props.data?.brandUuid))

// Form setup
const { submit } = useForm("assign-quantity-form");

// API requests
const req = useApiRequest();
const updateReq = useApiRequest();
const selectedBranch = ref("");
const assignedQuantity = ref("");

// Existing distributions management
const selectedDistribution = ref(null);
const editingDistribution = ref(null);
const editForm = ref({
  quantityAllocated: 0,
  priorityLevel: 4,
  expectedDeliveryDate: '',
  notes: ''
});

// Computed properties
const existingDistributions = computed(() => {
  return getAssignedQuantity.response.value || [];
});

const totalAssignedQuantity = computed(() => {
  return existingDistributions.value.reduce((total, dist) => total + (dist.quantityAllocated || 0), 0);
});

const remainingQuantity = computed(() => {
  return (props.data?.quantity || 0) - totalAssignedQuantity.value;
});

// Functions for managing distributions
function selectDistribution(distribution) {
  selectedDistribution.value = distribution;
  editingDistribution.value = null;
}

function startEditing(distribution) {
  editingDistribution.value = distribution.id;
  editForm.value = {
    quantityAllocated: distribution.quantityAllocated,
    priorityLevel: distribution.priorityLevel,
    expectedDeliveryDate: distribution.expectedDeliveryDate,
    notes: distribution.notes || ''
  };
}

function cancelEditing() {
  editingDistribution.value = null;
  editForm.value = {
    quantityAllocated: 0,
    priorityLevel: 4,
    expectedDeliveryDate: '',
    notes: ''
  };
}

function saveDistribution(distribution) {
  updateReq.send(
    () => updateDistribution(distribution.id, editForm.value),
    (res) => {
      if (res.success) {
        // Refresh the distributions list
        getAssignedQuantity.send(() => getDistributionByBrancdId(props.data?.purchaseOrderUuid));
        editingDistribution.value = null;
        toasted(true, "Distribution updated successfully");
      } else {
        toasted(false, "Failed to update distribution", res.error);
      }
    }
  );
}

function getPriorityColor(level) {
  const colors = {
    1: 'bg-red-100 text-red-800 border-red-200',
    2: 'bg-orange-100 text-orange-800 border-orange-200',
    3: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    4: 'bg-green-100 text-green-800 border-green-200'
  };
  return colors[level] || 'bg-gray-100 text-gray-800 border-gray-200';
}

function getPriorityLabel(level) {
  const labels = {
    1: 'Critical',
    2: 'High',
    3: 'Medium',
    4: 'Low'
  };
  return labels[level] || 'Unknown';
}

function submitAssignment({values}) {
  req.send(
    () =>
      orderDistribution({
        drugBrandUuid: props.data.brandUuid,
        purchaseOrderItemUuid: props.data.purchaseOrderUuid,
        ...values,
      }),
    (res) => {
      if (res.success) {
        // Refresh the distributions list
        getAssignedQuantity.send(() => getDistributionByBrancdId(props.data?.purchaseOrderUuid));
        // Reset form
        assignedQuantity.value = "";
        selectedBranch.value = "";
      }
      toasted(res.success, "Quantity assigned successfully", res.error);
    }
  );
}
</script>

<template>
  <ModalParent>
    <NewFormParent size="md" title="Assign Quantity for Receiving">
      <Form :inner="false" id="assign-quantity-form">
        <div class="flex flex-col gap-6 pt-2">
          <!-- Existing Distributions -->
          <div v-if="!getAssignedQuantity.pending.value && existingDistributions.length > 0" class="mb-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="font-semibold">Current Distributions</h3>
              <div class="text-sm text-gray-600">
                Total Assigned: <span class="font-medium text-blue-600">{{ totalAssignedQuantity }}</span> /
                <span class="font-medium">{{ props.data?.quantity }}</span>
              </div>
            </div>

            <!-- Horizontal scrollable list -->
            <div class="flex gap-4 overflow-x-auto pb-4">
              <div
                v-for="distribution in existingDistributions"
                :key="distribution.id"
                class="flex-shrink-0 w-80 border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                :class="[
                  selectedDistribution?.id === distribution.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200',
                  editingDistribution === distribution.id ? 'ring-2 ring-blue-300' : ''
                ]"
                @click="selectDistribution(distribution)"
              >
                <!-- Distribution Header -->
                <div class="flex justify-between items-start mb-3">
                  <div class="flex-1">
                    <h4 class="font-medium text-gray-900 truncate">{{ distribution.branch?.branchName }}</h4>
                    <p class="text-xs text-gray-500">{{ distribution.branch?.code }}</p>
                  </div>
                  <div class="flex gap-1">
                    <button
                      @click.stop="startEditing(distribution)"
                      class="text-blue-600 hover:text-blue-800 text-xs px-2 py-1 rounded"
                      :disabled="editingDistribution === distribution.id"
                    >
                      {{ editingDistribution === distribution.id ? 'Editing...' : 'Edit' }}
                    </button>
                  </div>
                </div>

                <!-- Editing Form -->
                <div v-if="editingDistribution === distribution.id" class="space-y-3">
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Quantity</label>
                    <input
                      v-model.number="editForm.quantityAllocated"
                      type="number"
                      :max="props.data?.quantity"
                      min="1"
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Priority</label>
                    <select
                      v-model.number="editForm.priorityLevel"
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option :value="1">Critical</option>
                      <option :value="2">High</option>
                      <option :value="3">Medium</option>
                      <option :value="4">Low</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Expected Date</label>
                    <input
                      v-model="editForm.expectedDeliveryDate"
                      type="date"
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Notes</label>
                    <textarea
                      v-model="editForm.notes"
                      rows="2"
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    ></textarea>
                  </div>
                  <div class="flex gap-2 pt-2">
                    <button
                      @click.stop="saveDistribution(distribution)"
                      :disabled="updateReq.pending.value"
                      class="flex-1 bg-blue-600 text-white text-xs py-1 px-2 rounded hover:bg-blue-700 disabled:opacity-50"
                    >
                      {{ updateReq.pending.value ? 'Saving...' : 'Save' }}
                    </button>
                    <button
                      @click.stop="cancelEditing()"
                      class="flex-1 bg-gray-300 text-gray-700 text-xs py-1 px-2 rounded hover:bg-gray-400"
                    >
                      Cancel
                    </button>
                  </div>
                </div>

                <!-- Display Mode -->
                <div v-else class="space-y-2">
                  <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-600">Quantity:</span>
                    <span class="font-medium text-sm">{{ distribution.quantityAllocated }} {{ props.data?.unit }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-600">Priority:</span>
                    <span
                      class="text-xs px-2 py-1 rounded-full border"
                      :class="getPriorityColor(distribution.priorityLevel)"
                    >
                      {{ getPriorityLabel(distribution.priorityLevel) }}
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-600">Status:</span>
                    <span
                      class="text-xs px-2 py-1 rounded-full"
                      :class="{
                        'bg-green-100 text-green-800': distribution.distributionStatus === 'Completed',
                        'bg-yellow-100 text-yellow-800': distribution.distributionStatus === 'Pending',
                        'bg-blue-100 text-blue-800': distribution.distributionStatus === 'In Progress',
                        'bg-gray-100 text-gray-800': !distribution.distributionStatus
                      }"
                    >
                      {{ distribution.distributionStatus || 'Unknown' }}
                    </span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-600">Expected:</span>
                    <span class="text-xs">{{ new Date(distribution.expectedDeliveryDate).toLocaleDateString() }}</span>
                  </div>
                  <div v-if="distribution.notes" class="mt-2">
                    <p class="text-xs text-gray-600 italic">{{ distribution.notes }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Loading state -->
          <div v-else-if="getAssignedQuantity.pending.value" class="mb-6">
            <div class="flex items-center justify-center py-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span class="ml-2 text-gray-600">Loading existing distributions...</span>
            </div>
          </div>

          <!-- Item Information Display -->
          <h3 class="font-semibold mb-3">Purchase Order Item Details</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-600">Item Name:</span>
              <span class="font-medium">{{ props.data?.itemName }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">Unit:</span>
              <span class="font-medium">{{ props.data?.unit }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">Total Quantity:</span>
              <span class="font-medium">{{ props.data?.quantity }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">Already Assigned:</span>
              <span class="font-medium text-orange-600">{{ totalAssignedQuantity }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">Available for Assignment:</span>
              <span class="font-bold text-green-600">{{ remainingQuantity }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">Unit Price:</span>
              <span class="font-medium">${{ props.data?.unitPrice || 0 }}</span>
            </div>
          </div>

          <!-- Assignment Form -->
          <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <Input
                name="quantityAllocated"
                v-model="assignedQuantity"
                label="Quantity to Assign for Receiving"
                focus
                :validation="{
                  required: true,
                  num_minmax: {
                    args: [1, remainingQuantity],
                  },
                }"
                :attributes="{
                  placeholder: `Enter quantity (Max: ${remainingQuantity})`,
                  ...(
                    remainingQuantity <= 0 ?
                    {disabled: true} :{}
                  )
                   
                }"
              />
              <div v-if="remainingQuantity <= 0" class="col-span-2 text-sm text-red-600 bg-red-50 p-2 rounded">
                ⚠️ All quantities have been assigned. Edit existing distributions to reassign quantities.
              </div>
              <Input
                name="expectedDeliveryDate"
                label="Expected Delivery Date"
                validation="required|greaterThanToday"
                :attributes="{
                  type: 'date',
                }"
              />
              <Select
                name="priorityLevel"
                label="Priority Level"
                value="4"
                :options="['4', '3', '2', '1']"
                validation="required"
              />
            </div>
            <SearchSelectInput
              label="Branch"
              position="left-bottom"
              :onChange="
                (value) => {
                  selectedBranch = value;
                }
              "
              name="branchUuid"
              :validation="{
                required: true,
              }"
              :option="{ label: 'branchName', value: 'branchUuid' }"
              :search-cb="getAllBranches"
              placeholder="Search and select branch"
            />
            <Textarea
              name="notes"
              label="Notes (Optional)"
              :attributes="{
                placeholder: 'Add any additional notes or instructions...',
                rows: 3,
              }"
            />
            <div
              v-if="selectedBranch && assignedQuantity > 0 && remainingQuantity > 0"
              class="bg-blue-50 p-4 rounded-lg"
            >
              <h4 class="font-medium text-blue-800 mb-2">Assignment Summary</h4>
              <div class="text-sm text-blue-700">
                <p>
                  <strong>{{ assignedQuantity }}</strong>
                  {{ props.data?.unit }} of
                  <strong>{{ props.data?.itemName }}</strong>
                </p>
                <p>
                  Will be assigned to:
                  <strong>{{ selectedBranch?.branchName }}</strong>
                </p>
                <p>
                  Estimated Value:
                  <strong
                    >${{
                      (assignedQuantity * (props.data?.unitPrice || 0)).toFixed(
                        2
                      )
                    }}</strong
                  >
                </p>
                <p>
                  Remaining after this assignment:
                  <strong>{{ remainingQuantity - assignedQuantity }}</strong>
                  {{ props.data?.unit }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </Form>

      <template #bottom>
        <div class="flex justify-end gap-3 p-2">
          <Button
            @click="closeModal()"
            class="px-6 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            @click.prevent.stop="submit(submitAssignment)"
            type="primary"
            :pending="req.pending.value"
            :disabled="remainingQuantity <= 0"
            class="px-6 py-2"
            :class="{ 'opacity-50 cursor-not-allowed': remainingQuantity <= 0 }"
          >
            {{ remainingQuantity <= 0 ? 'No Quantity Available' : 'Assign Quantity' }}
          </Button>
        </div>
      </template>
    </NewFormParent>
  </ModalParent>
</template>

<style scoped>
.custom-input :deep(input:focus) {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.custom-input :deep(.focus-within\:sys-focus:focus-within) {
  border-color: #3b82f6 !important;
}

.custom-input :deep(textarea:focus) {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}
</style>
