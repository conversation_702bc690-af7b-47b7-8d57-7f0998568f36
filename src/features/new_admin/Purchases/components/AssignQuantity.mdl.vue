<script setup>
import Input from "@/components/new_form_elements/Input.vue";
import Textarea from "@/components/new_form_elements/Textarea.vue";
import SearchSelect from "@/components/SearchSelect.vue";
import NewFormParent from "../../role/components/NewFormParent.vue";
import ModalParent from "@/components/ModalParent.vue";
import Button from "@/components/Button.vue";
import Form from "@/new_form_builder/Form.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { closeModal, openModal } from "@customizer/modal-x";
import { toasted } from "@/utils/utils";
import { ref, computed } from "vue";
import { useForm } from "@/new_form_builder/useForm";
import { getAllBranches } from "../../Branches/Api/BranchesApi";
import { updatePurchaseOrderItem } from "../Api/purchaseOrderItemApi";
import SearchSelectInput from "@/components/SearchSelectInput.vue";
import { getDistributionByBrancdId, orderDistribution } from "../Api/purchaseOrderApi";
import Select from "@/components/new_form_elements/Select.vue";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const getAssignedQuantity = useApiRequest()

getAssignedQuantity.send(() => getDistributionByBrancdId(props.data?.purchaseOrderUuid))

// Form setup
const { submit } = useForm("assign-quantity-form");

// API requests
const req = useApiRequest();
const selectedBranch = ref("");
const assignedQuantity = ref("");

function submitAssignment({values}) {
  req.send(
    () =>
      orderDistribution({
        drugBrandUuid: props.data.brandUuid,
        purchaseOrderItemUuid: props.data.purchaseOrderUuid,
        ...values,
      }),
    (res) => {
      if (res.success) {
        closeModal(res.data);
      }
      toasted(res.success, "Quantity assigned successfully", res.error);
    }
  );
}
</script>

<template>
  <ModalParent>
    <NewFormParent size="md" title="Assign Quantity for Receiving">
      <Form :inner="false" id="assign-quantity-form">
        <div class="flex flex-col gap-6 pt-2">
          <!-- Item Information Display -->
          <h3 class="font-semibold mb-3">Purchase Order Item Details</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-600">Item Name:</span>
              <span class="font-medium">{{ props.data?.itemName }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">Unit:</span>
              <span class="font-medium">{{ props.data?.unit }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">Available Quantity:</span>
              <span class="font-bold text-green-600">{{
                props.data?.quantity
              }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-600">Unit Price:</span>
              <span class="font-medium">${{ props.data?.unitPrice || 0 }}</span>
            </div>
          </div>

          <!-- Assignment Form -->
          <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <Input
                name="quantityAllocated"
                v-model="assignedQuantity"
                label="Quantity to Assign for Receiving"
                focus
                :validation="{
                  required: true,
                  num_minmax: {
                    args: [1, props.data?.quantity],
                  },
                }"
                :attributes="{
                  placeholder: `Enter quantity (Max: ${props.data?.quantity})`,
                }"
              />
              <Input
                name="expectedDeliveryDate"
                label="Expected Delivery Date"
                validation="required|greaterThanToday"
                :attributes="{
                  type: 'date',
                }"
              />
              <Select 
                name="priorityLevel"
                label="Priority Level"
                value="4"
                :options="['4', '3', '2', '1']"
                validation="required"
              />
            </div>
            <SearchSelectInput
              label="Branch"
              position="left-bottom"
              :onChange="
                (value) => {
                  selectedBranch = value;
                }
              "
              name="branchUuid"
              :validation="{
                required: true,
              }"
              :option="{ label: 'branchName', value: 'branchUuid' }"
              :search-cb="getAllBranches"
              placeholder="Search and select branch"
            />
            <Textarea
              name="notes"
              label="Notes (Optional)"
              :attributes="{
                placeholder: 'Add any additional notes or instructions...',
                rows: 3,
              }"
            />
            <div
              v-if="selectedBranch && assignedQuantity > 0"
              class="bg-blue-50 p-4 rounded-lg"
            >
              <h4 class="font-medium text-blue-800 mb-2">Assignment Summary</h4>
              <div class="text-sm text-blue-700">
                <p>
                  <strong>{{ assignedQuantity }}</strong>
                  {{ props.data?.unit }} of
                  <strong>{{ props.data?.itemName }}</strong>
                </p>
                <p>
                  Will be assigned to:
                  <strong>{{ selectedBranch?.branchName }}</strong>
                </p>
                <p>
                  Estimated Value:
                  <strong
                    >${{
                      (assignedQuantity * (props.data?.unitPrice || 0)).toFixed(
                        2
                      )
                    }}</strong
                  >
                </p>
              </div>
            </div>
          </div>
        </div>
      </Form>

      <template #bottom>
        <div class="flex justify-end gap-3 p-2">
          <Button
            @click="closeModal()"
            class="px-6 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            @click.prevent.stop="submit(submitAssignment)"
            type="primary"
            :pending="req.pending.value"
            class="px-6 py-2"
          >
            Assign Quantity
          </Button>
        </div>
      </template>
    </NewFormParent>
  </ModalParent>
</template>

<style scoped>
.custom-input :deep(input:focus) {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.custom-input :deep(.focus-within\:sys-focus:focus-within) {
  border-color: #3b82f6 !important;
}

.custom-input :deep(textarea:focus) {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}
</style>
