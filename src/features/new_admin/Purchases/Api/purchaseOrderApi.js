import { getApi } from "@/utils/utils";

const api = getApi('/purchase-order');

export function createPurchaseOrder(data) {
  return api
    .addAuthenticationHeader()
    .post('', data);
}

export function updatePurchaseOrder(id, data) {
  return api
    .addAuthenticationHeader()
    .put(`/update/${id}`, data);
}

export function getAllPurchaseOrder(query = {}) {
  return api.addAuthenticationHeader().get('/all', {
    params: query
  })
}

export function orderDistribution(data) {
  return api.addAuthenticationHeader().post('/distributions', data)
}

export function getDistributionByBrancdId(id) {
  return api.addAuthenticationHeader().get(`/distributions/drug-brand/${id}`)
}

export function getPurchaseOrderById(id) {
  return api.addAuthenticationHeader().get(`/${id}`)
}

export function updateOrderByBatch(id, data) {
  return api.addAuthenticationHeader().put(`/batch-update/${id}`, data)
}

export function updatePurchaseOrderByStatus(id, status) {
  return api.addAuthenticationHeader().put(`/batch-update-status/${id}`, status)
}

export function assignQuantityTobranch(id, data) {
  return api.addAuthenticationHeader().put(`/assign-quantity/${id}`, data)
}