<script setup>
import Table from "@/components/Table.vue";
import icons from "@/utils/icons";
import Button from "@/components/Button.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import { checkDrugAvailability } from "@/features/paper_prescription/api/paperPrescriptionApi";
import { formatCurrency } from "@/utils/utils";
import { openModal } from "@customizer/modal-x";
import DefaultPage from "@/components/DefaultPage.vue";

const pagination = usePaginationcopy({
  cb: checkDrugAvailability,
});
</script>

<template>
  <DefaultPage>
    <div class="flex items-end justify-between gap-2">
      <div class="border rounded-md h-11 px-3 col-span-4">
        <input
          class="h-full bg-transparent w-full"
          name="search"
          v-model="pagination.search.value"
          placeholder="Serach Drug"
        />
      </div>
    </div>
    <hr />
    <div class="rounded-lg p-4 gap-4 flex flex-1 flex-col bg-[#F8F8F8]">
      <div class="bg-white">
        <Table
          :pending="pagination.pending.value"
          :headers="{
            head: [
              'Drug Name',
              'Brand Name',
              'Retail Price',
              'Quantity',
              'actions',
            ],
            row: [
              'drugName',
              'drugBrandName',
              'sellingUnitPrice',
              'totalAmount',
            ],
          }"
          :rows="pagination.data.value?.content || []"
          :cells="{
            sellingUnitPrice: formatCurrency,
            strength: (_, row) => {
              return row?.strength
                ? `${row.strength} ${row.unit}`
                : `${row.strength || 'No Strength'}`;
            },
            totalAmount: (_, row) => {
              return `${row?.totalAmount ?? ''} ${row?.drugUnit ?? ''}`;
            },
          }"
        >
          <template #actions="{ row }">
            <Button @click="openModal('UpdatePrice', row)" size="xs" type="edge"
              >Update Retail Price</Button
            >
          </template>
        </Table>
      </div>
    </div>
  </DefaultPage>
</template>
