import { getApi, getQueryFormObject } from "@/utils/utils.js";

const api = getApi('/drug-units');

export function getAllUnits(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`/all${qr}`);
}

export function createUnit(data) {
  return api.addAuthenticationHeader().post(`/create`, data);
}

export function getUnitById(id) {
  return api.addAuthenticationHeader().get(`/${id}`);
}

export function updateUnitById(id, data) {
  return api.addAuthenticationHeader().put(`/update/${id}`, data);
}

export function deleteUnitById(id) {
  return api.addAuthenticationHeader().delete(`/delete/${id}`);
}

export function updateUnitStatus(id, status) {
  return api.addAuthenticationHeader().patch(`/status/${id}`, { status });
}
