<script setup>
import BaseIcon from "@/components/base/BaseIcon.vue";
import Dropdown from "@/components/Dropdown.vue";
import Table from "@/components/Table.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import { openModal } from "@customizer/modal-x";
import {
  mdiChevronDown,
  mdiImport,
  mdiMagnify,
  mdiPencil,
  mdiPlus,
  mdiPlusCircle,
} from "@mdi/js";
import { getAllDrug } from '@/features/new_admin/drugs/Api/drugApi';
import { useDrugs } from '@/features/new_admin/drugs/store/drugStore';
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import { ref } from "vue";
import Button from "@/components/Button.vue";
import DefaultPage from "@/components/DefaultPage.vue";

const drugStore = useDrugs();
const pagination = usePaginationcopy({
  store: drugStore,
  cb: (params) => getAllDrug(params),
});
</script>

<template>
  <DefaultPage>
    <div class="flex justify-between h-14">
      <div class="flex border rounded px-2 items-center">
        <input
          v-model="pagination.search.value"
          class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
          placeholder="Search Drugs "
        />
        <!-- @keydown.enter="fetchInstitution"  -->
        <BaseIcon :path="mdiMagnify" :size="20" />
      </div>
      <div class="flex gap-3">
        <button
          @click="openModal('ImportDrugs')"
          class="flex gap-4 bg-primary text-white px-4 border rounded-lg h-14 items-center"
        >
          <BaseIcon :path="mdiImport" size="lg" />
          <p>Import</p>
        </button>
        <button
          @click.prevent="openModal('AddDrug')"
          class="flex gap-4 bg-primary text-white px-4 border rounded-lg h-14 items-center"
        >
          <BaseIcon :path="mdiPlus" :size="20" />
          <p class="opacity-80">Add Drug</p>
        </button>
      </div>
    </div>
    <Table
      :headers="{
        head: [
          'Drug Name',
          'Dosage Form',
          'Actions',
        ],
        row: ['drugName', 'dosageForm'],
      }"
      :cells="{
        brand: (brand) => {
          return brand.reduce((names, el) => {
            names += el.drugBrandName + ', ';
            return names;
          }, '');
        },
      }"
      :pending="pagination.pending.value"
      :Fallback="TableRowSkeleton"
      :rows="drugStore.drugs"
    >
      <template #actions="{ row }">
        <div class="flex gap-4">
          <Button
            @click="openModal('EditDrugs', row)"
            class="rounded-lg bg-gray-600 text-white px-2 py-1 border-gray-300 flex gap-1 items-center"
            size="xs"
          >
            <BaseIcon :path="mdiPencil" />
            Edit
          </Button>
          <Button
            @click="openModal('AddBrand', row)"
            class="rounded-lg bg-gray-600 text-white px-2 py-1 border-gray-300 flex gap-1 items-center"
            size="xs"
          >
            <BaseIcon :path="mdiPlusCircle" />
            Add Brand
          </Button>
        </div>
      </template>
    </Table>
  </DefaultPage>
</template>
