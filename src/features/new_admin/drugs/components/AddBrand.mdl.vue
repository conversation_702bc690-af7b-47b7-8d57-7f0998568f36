<script setup lang="ts">
import BaseIcon from '@/components/base/BaseIcon.vue';
import NewFormModal from '@/components/NewFormModal.vue';
import { closeModal } from '@customizer/modal-x';
import { mdiClose, mdiPencil } from '@mdi/js';
import DrugForm from '../form/DrugForm.vue';
import NewFormParent from '../../role/components/NewFormParent.vue';
import BrandForm from '../form/BrandForm.vue';
import Button from '@/components/Button.vue';

const props = defineProps({
    data: Object
})
</script>

<template>
    <div class="bg-black/50 min-h-full w-full p-10 grid place-items-center">
        <NewFormParent size="xs" title="Add Brand">
            <!-- <div class="w-[8rem] flex place-items-end">
                <Button class="rounded-lg bg-gray-600 text-white p-4 border-gray-300 flex gap-1 items-center" size="xs">
                    <BaseIcon :path="mdiPencil" />
                    Edit Brand
                </Button>
            </div> -->
            <div class="p-8 gap-10 bg-white rounded-lg flex flex-col">
                <BrandForm :drug="props.data" />
            </div>
        </NewFormParent>
    </div>
</template>