<script setup>
import { data } from 'autoprefixer';
import NewFormParent from '../../role/components/NewFormParent.vue';
import EditDrugForm from '../form/EditDrugForm.vue';

const props = defineProps({
    data: Object
})

console.log(props.data);

</script>

<template>
    <div class="bg-black/50 min-h-full w-full p-10 grid place-items-center">
        <NewFormParent size="lg" title="Edit Drug">
            <div class="p-8 gap-10 bg-white rounded-lg flex flex-col">
                <EditDrugForm :drug="props.data" />
            </div>
        </NewFormParent>
    </div>
</template>