import ApiService from "@/service/ApiService";
import { getQueryFormObject } from "@/utils/utils";

const api = new ApiService();
const path = "/stock/drug";
const path1 = "/stock/drugBrands";

export function getAllDrug(query = {}) {
  const qr = getQueryFormObject(query);
  return api.addAuthenticationHeader().get(`${path}/all${qr}`);
}
export function craeteDrug(data) {
  return api.addAuthenticationHeader().post(`${path}/createDrug`, data);
}
export function updateDrug(id, data) {
  return api.addAuthenticationHeader().put(`${path}/${id}`, data);
}
export function importDrug(data) {
  return api.addAuthenticationHeader().post(`${path}/import`, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
export function craeteBrand(id, data) {
  return api.addAuthenticationHeader().post(`${path1}/addBrand/${id}`, data);
}
