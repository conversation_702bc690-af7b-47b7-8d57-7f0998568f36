<script setup>
import { ref } from 'vue';
import BaseIcon from '@/components/base/BaseIcon.vue';
import { importDrug } from '../Api/drugApi';
import { useApiRequest } from '@/composables/useApiRequest';
import { useDrugs } from '../store/drugStore';
import { toasted } from '@/utils/utils';
import Input from '@/components/new_form_elements/Input.vue';
import Form from '@/new_form_builder/Form.vue';
import { mdiImport, mdiPlus } from '@mdi/js';
import { closeModal } from '@customizer/modal-x';

const req = useApiRequest()
const drugStore = useDrugs()
const selectedFile = ref(null);
const fileError = ref(false);
const fileInput = ref(null);


function handleFileUpload(event) {
    const file = event.target.files[0];
    if (file) {
        selectedFile.value = file;
        fileError.value = false;
    }
}

function importExcel() {
    if (!selectedFile.value) {
        fileError.value = true; // Set error state if no file is selected
        fileInput.value.focus(); // Optionally focus the input field
        return;
    }

    const formData = new FormData();
    formData.append('file', selectedFile.value);

    req.send(
        () => importDrug(formData),
        (res) => {
            if (res.success) {
                drugStore.set(formData);
            }
            toasted(res.success, 'Imported Successfully', res.error);
            closeModal();
        })

    // try {
    //     const response = await importDrug(formData); // Call your API function
    //     if (response.success) {
    //         console.log('Import successful:', response);
    //         // Optionally, refresh your drug list or show a success message
    //     } else {
    //         console.error('Import failed:', response.error);
    //     }
    // } catch (error) {
    //     console.error('Error importing Excel:', error);
    // }
}
</script>
<template>
    <div class="flex gap-3 px-10 justify-between h-14">
        <input type="file" ref="fileInput" @change="handleFileUpload" accept=".csv"
            class="border rounded px-2 flex items-center" />
        <button @click.prevent="importExcel"
            class="flex gap-4 bg-secondary px-4   border rounded h-14 items-center hover:bg-primary">
            <BaseIcon class="text-white" :path="mdiImport" :size="20" />
            <p class="text-white text-center">Import Excel</p>
        </button>
    </div>
    <p v-if="fileError" class="text-red-500">Please select a file to import.</p>
</template>