<script setup>
import Button from '@/components/Button.vue';
import LayeredPages from '@/components/LayeredPages.vue';
import { usePagination } from '@/composables/usePagination';
import { computed, onMounted, ref } from 'vue';
import Table from '@/components/Table.vue';
import TableRowSkeleton from '@/skeletons/TableRowSkeleton.vue';
import { useCompoundingStore } from './store/compoundingStore';
import { checkMedicineAvailability } from './api/compoundingApi';
import { closeModal } from '@customizer/modal-x';
import NewFormParent from '@/features/new_admin/role/components/NewFormParent.vue';

const compoundingStore = useCompoundingStore();
const pagination = usePagination({
    auto: false,
    cb: checkMedicineAvailability,
});

const search = ref('')
const showLayeredPages = ref(false);
const searchTerm = ref('');
const mockData = ref([
    { inventoryUuid: 1, ingredientName: 'Vaseline', category: 'Base', storeStatus: '500mg', quantity: 10, actions: '' },
    { inventoryUuid: 2, ingredientName: 'Nivea', category: 'Base', storeStatus: '200mg', quantity: 15, },
    { inventoryUuid: 3, ingredientName: 'Metronidazole', category: 'Active Ingrediant', storeStatus: '250mg/5ml', quantity: 25, }
]);

const filteredData = computed(() => {
    return mockData.value.filter((drug) =>
        drug.ingredientName.toLowerCase().includes(searchTerm.value.toLowerCase())
    );
});

// const search = () => {
//     pagination.data.value = { content: filteredData.value };
// };

function fetchDrugs(ev) {
    pagination.search.value = search.value;
}

// function addMed(order, type) {
//     delete order.Contraindication;
//     delete order.description;

//     compoundingStore.addToList({
//         type,
//         ...order
//     });
// }

// onMounted(() => {
//     if (compoundingStore.done) {
//         compoundingStore.reset()
//     }
// })
function selectDrug(selectedDrug) {
    closeModal(selectedDrug.inventoryUuid); // Close modal and return inventoryUuid
}

</script>
<template>
    <div class="bg-black/50 min-h-full p-10 grid place-items-center">
        <NewFormParent title="Search For Cup" size="lg">
            <div class="flex flex-1 h-full gap-5 overflow-hidden">
                <div class="flex-1 flex justify-center h-full overflow-hidden">
                    <div class="flex flex-col gap-4 w-[90%] h-full">
                        <div
                            class="flex justify-between md:gap-2 items-center px-2 rounded border-primary border-2  min-h-drug-search-height">
                            <input v-focus v-model="search" @keydown="fetchDrugs"
                                class="px-2 bg-transparent flex-1 h-12" placeholder="Search Drug" />
                            <!-- @click="search" -->
                            <Button @click="fetchDrugs" size="md" type="primary"> Search </Button>
                        </div>
                        <div v-if="!pagination.pending.value && !pagination.data.value"
                            class="flex-1 flex flex-col gap-12 items-center">
                            <img src="/src/assets/img/search_med.svg" />
                            <div class="flex flex-col gap-4 items-center">
                                <p class="font-medium text-md">
                                    Please search medicine availability and price.
                                </p>
                                <p class="text-xs px-20">
                                    Note: you can search a medicine using medicine or generic name.
                                </p>
                            </div>
                        </div>

                        <div v-else class="overflow-auto show-scrollbar">
                            <Table :pending="pagination.pending.value" :headers="{
                                head: [
                                    'Ingredient code',
                                    'Ingredient Name',
                                    'Category',
                                    'Dosage Form',
                                    'Quantity',
                                    'actions',
                                ],
                                row: [
                                    'drugCode',
                                    'drugName',
                                    'category',
                                    'dosageForm',
                                    'totalAmount',
                                ],
                            }" :rows="(pagination.data.value?.content || []).map((el) => {
                                return {
                                    dosageForm: `Accurate dose`,
                                    ...el,
                                };
                            })
                                " :cells="{

                                    list_price: (price) => {
                                        return formatCurrency(price);
                                    },
                                }" :Fallback="TableRowSkeleton">
                                <template #actions="{ row }">
                                    <div class="flex gap-2">
                                        <Button @click="selectDrug(row)" class="text-sm" size="xs" type="secondary">
                                            Select
                                        </Button>
                                    </div>
                                </template>
                            </Table>
                        </div>
                    </div>
                </div>
            </div>
        </NewFormParent>
    </div>
</template>