<script setup>
import <PERSON><PERSON> from "@/components/Button.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import NewFormParent from "@/features/new_admin/role/components/NewFormParent.vue";
import { toasted } from "@/utils/utils";
import { closeModal, openModal } from "@customizer/modal-x";
import { updateLossAdjustment } from "../api/lossAdjustmentApi";
import { useLossAdjustmentStore } from "../store/lossAdjustmentStore";
import LossAdjustmentForm from "../form/LossAdjustmentForm.vue";

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});

const lossAdjustmentStore = useLossAdjustmentStore();
const req = useApiRequest();

function updateAdjustment(values) {
  openModal('Confirmation', {
    title: 'Update Loss Adjustment',
    message: 'Are you sure you want to update this loss adjustment?'
  },
  (res) => {
    if (res) {
      req.send(
        () => updateLossAdjustment(props.data.adjustmentUuid, values),
        (res) => {
          if (res.success) {
            lossAdjustmentStore.update(props.data.adjustmentUuid, { ...props.data, ...values });
          }
          toasted(res.success, "Loss adjustment updated successfully", res.error);
          closeModal(true);
        }
      );
    }
  });
}
</script>

<template>
  <div class="bg-black/50 h-55 p-10 min-h-full grid place-items-center">
    <NewFormParent size="xs" title="Edit Loss Adjustment">
      <div class="p-6">
        <LossAdjustmentForm 
          :data="props.data" 
          :onSubmit="updateAdjustment"
        >
          <template #actions="{ submit }">
            <Button @click="closeModal" type="edge">
              Cancel
            </Button>
            <Button 
              :pending="req.pending.value" 
              @click.prevent="submit(updateAdjustment)" 
              type="primary"
            >
              Update
            </Button>
          </template>
        </LossAdjustmentForm>
      </div>
    </NewFormParent>
  </div>
</template>
