<script setup>
import Button from "@/components/Button.vue";
import Input from "@/components/new_form_elements/Input.vue";
import Select from "@/components/new_form_elements/Select.vue";
import Textarea from "@/components/new_form_elements/Textarea.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import NewFormParent from "@/features/new_admin/role/components/NewFormParent.vue";
import Form from "@/new_form_builder/Form.vue";
import { toasted } from "@/utils/utils";
import { closeModal, openModal } from "@customizer/modal-x";
import { ref } from "vue";
import { createLossAdjustment } from "../api/lossAdjustmentApi";
import { useLossAdjustmentStore } from "../store/lossAdjustmentStore";
import LossAdjustmentForm from "../form/LossAdjustmentForm.vue";
import { useForm } from "@/new_form_builder/useForm";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const request = useApiRequest();

function submitAdjustment({ values }) {
  openModal(
    "Confirmation",
    {
      title: "Loss Adjustment",
      message: "Are you sure you want to submit this loss adjustment?",
    },
    (res) => {
      if (res) {
        request.send(
          () =>
            createLossAdjustment(props.data.inventoryUuid, {
              ...values,
              amount: parseInt(values.amount),
              inventoryUuid: props.data.inventoryUuid,
            }),
          (res) => {
            toasted(
              res.success,
              "Loss adjustment submitted successfully",
              res.error
            );
            if (res.success) {
              closeModal();
            }
          }
        );
      }
    }
  );
}

const { submit } = useForm("lossAdjustmentForm");
</script>

<template>
  <div class="bg-black/50 h-55 p-10 min-h-full w-full grid place-items-center">
    <NewFormParent size="md" title="Loss / Adjustment">
      <div class="grid grid-cols-2 gap-4">
        <div class="flex flex-col gap-1">
          <label class="text-sm">Medicine Name</label>
          <p
            class="border bg-base-clr-1 rounded-md px-3 h-11 flex place-content-start items-center"
          >
            {{ props.data?.drugName || "" }}
          </p>
        </div>

        <div class="flex flex-col gap-1">
          <label class="text-sm">Batch Number</label>
          <p
            class="border bg-base-clr-1 rounded-md px-3 h-11 flex place-content-start items-center"
          >
            {{ props.data?.batchNumber || "" }}
          </p>
        </div>

        <div class="flex flex-col gap-1">
          <label class="text-sm">Stock On Hand (SOH)</label>
          <p
            class="border bg-base-clr-1 rounded-md px-3 h-11 flex place-content-start items-center"
          >
            {{ props.data?.totalAmount || 0 }}
          </p>
        </div>
      </div>
      <LossAdjustmentForm />
      <template #bottom>
        <div class="flex justify-end gap-3 p-2">
          <Button @click="closeModal()" type="edge"> Cancel </Button>
          <Button
            :pending="request.pending.value"
            @click.prevent="submit(submitAdjustment)"
            type="primary"
          >
            Submit
          </Button>
        </div>
      </template>
    </NewFormParent>
  </div>
</template>
