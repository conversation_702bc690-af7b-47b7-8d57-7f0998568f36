<script setup lang="ts">
import Button from '@/components/Button.vue';
import Input from '@/components/new_form_elements/Input.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import NewFormParent from '@/features/new_admin/role/components/NewFormParent.vue';
import { updateMinandMaxById } from '@/features/StoreKeeper/api/InventoryApi';
import { useStockStore } from '@/features/StoreKeeper/store/StockStore';
import Form from '@/new_form_builder/Form.vue';
import { toasted } from '@/utils/utils';
import { closeModal } from '@customizer/modal-x';
const props = defineProps({
    data: Object
})
const stockStore = useStockStore()
const req = useApiRequest()
function update({ values }) {
    req.send(() => updateMinandMaxById(props.data?.inventoryUuid, values),
        (res) => {
            if (res.success) {
                stockStore.update(props.data?.inventoryUuid, { ...props.data, ...values })
            }
            toasted(res.success, 'Update Successfully', res.error);
            closeModal();
        })
}
</script>

<template>
    <div class="bg-black/50 h-55 p-10 min-h-full  grid place-items-center">
        <NewFormParent size='xs' :style="{ width: '25rem' }" title="Add Minimum">
            <Form class="flex flex-col gap-3 pt-5" v-slot="{ submit }" id="miniform">
                <Input name="minima" label="Minimum" validation="required" :attributes="{
                    placeholder: 'minimum'
                }" />
                <Button @click.prevent="submit(update)" size="md" class="text-sm text-white rounded bg-secondary">
                    Update
                </Button>
            </Form>
        </NewFormParent>
    </div>
</template>