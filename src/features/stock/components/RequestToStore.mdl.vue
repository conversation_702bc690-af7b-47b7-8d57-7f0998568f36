<script setup>
import BaseIcon from "@/components/base/BaseIcon.vue";
import Button from "@/components/Button.vue";
import Input from "@/components/new_form_elements/Input.vue";
import Select from "@/components/new_form_elements/Select.vue";
import Table from "@/components/Table.vue";
import NewFormParent from "@/features/new_admin/role/components/NewFormParent.vue";
import PaperPrescriptionStatusCell from "@/features/paper_prescription/components/PaperPrescriptionStatusCell.vue";
import { useStockStore } from "@/features/StoreKeeper/store/StockStore";
import Form from "@/new_form_builder/Form.vue";
import icons from "@/utils/icons";
import StockExDateTableCell from "./StockExDateTableCell.vue";
import { formatCurrency, RequestOptions, toasted } from "@/utils/utils";
import TableQuantityInput from "./TableQuantityInput.vue";
import { transferRequest } from "../api/stockApi";
import { useApiRequest } from "@/composables/useApiRequest";
import { closeModal, openModal } from "@customizer/modal-x";
import { regularRequest } from "../api/regularRequestApi";

const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
});

console.log(props.data);
const stockStore = useStockStore();
const request = useApiRequest();

function drugrequest({ values }) {
  openModal(
    "Confirmation",
    {
      title: "Request",
      message: "Are you sure you want to request?",
    },
    (res) => {
      if (res) {
        request.send(
          () =>
            regularRequest(
              {
                status: RequestOptions.Emergency,
              },
              Object.values(values)
            ),
          (res) => {
            toasted(res.success, "Request successfully", res.error);
            closeModal(true);
          }
        );
      }
    }
  );
}
</script>
<template>
  <div class="bg-black/50 h-55 p-10 min-h-full w-full grid place-items-center">
    <NewFormParent size="xs" title="Request Store">
      <template #left-actions>
        <i class="text-black" v-html="icons.leftArrow" />
      </template>
      <Form v-slot="{ submit }" id="quantity" class="flex flex-col gap-4">
        <div class="flex items-center justify-between">
          <p>List of Requests</p>
          <Button
            :pending="request.pending.value"
            @click.prevent="submit(drugrequest)"
            type="primary"
          >
            Submit
          </Button>
        </div>
        <Table
          :lastCol="true"
          :headers="{
            head: [
              'Batch Number',
              'Drug Code',
              'Medicine Name',
              'Dosage form',
              'Strength',
              'Drug Unit',
              'Brand Name',
              'Price',
              'EXP. Date',
              'Quantity',
            ],
            row: [
              'batchNumber',
              'drugCode',
              'drugName',
              'dosageForm',
              'strength',
              'drugUnit',
              'drugBrandName',
              'sellingUnitPrice',
              'expDate',
              'totalAmount',
            ],
          }"
          :rows="
            (props.data || [])
              .reduce((drugs, drug) => {
                if (stockStore.checkExpirdity(drug.drugUuid)) {
                  drugs.unshift(drug);
                } else {
                  drugs.push(drug);
                }
                return drugs;
              }, [])
              .map((el) => {
                return {
                  dosageForm: `Accurate dose`,
                  ...el,
                };
              })
          "
          :cells="{
            totalAmount: PaperPrescriptionStatusCell,
            sellingUnitPrice: (price) => {
              return formatCurrency(price);
            },
            expDate: StockExDateTableCell,
          }"
        >
          <template #headerLast>
            <p class="text-center">Quantity</p>
          </template>
          <template #lastCol="{ row }">
            <TableQuantityInput
              :name="row.batchNumber"
              :value="row"
              validation="required"
            />
          </template>
        </Table>
      </Form>
    </NewFormParent>
  </div>
</template>
