<script setup>
import BaseIcon from "@/components/base/BaseIcon.vue";
import Button from "@/components/Button.vue";
import Table from "@/components/Table.vue";
import { usePagination } from "@/composables/usePagination";
import PaperPrescriptionStatusCell from "@/features/paper_prescription/components/PaperPrescriptionStatusCell.vue";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import {
  BatchStatus,
  formatCurrency,
  openNewTab,
  secondDateFormat,
} from "@/utils/utils";
import { openModal } from "@customizer/modal-x";
import {
  mdiArrowDown,
  mdiCartOutline,
  mdiCircleOffOutline,
  mdiClockAlertOutline,
  mdiCurrencyUsd,
  mdiDotsVertical,
  mdiHistory,
  mdiMagnify,
  mdiMenuDown,
  mdiPencil,
  mdiPlus,
  mdiSend,
  mdiTransferLeft,
  mdiTransferRight,
} from "@mdi/js";
import { useRouter } from "vue-router";
import {
  checkMedicineAvailability,
  getMedicineByStatus,
} from "../api/stockApi";
import { useStockStore } from "@/features/StoreKeeper/store/StockStore";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import StockExDateTableCell from "../components/StockExDateTableCell.vue";
import Dropdown from "@/components/Dropdown.vue";
import RedTablerow from "../components/RedTablerow.vue";
import { ref, watch } from "vue";
import { getAllRequest } from "../api/regularRequestApi";
import { checkAvailability } from "@/features/paper_prescription/api/paperPrescriptionApi";

const router = useRouter();
const stockStore = useStockStore();

const pagination = usePaginationcopy({
  store: stockStore,
  cb: (params) => checkMedicineAvailability(params)
});

const selected = ref([]);
</script>

<template>
  <div class="h-full w-full flex flex-col gap-6 px-20 py-4">
    <div class="flex justify-between h-14">
      <div class="flex border rounded px-2 items-center">
        <input
          v-model="pagination.search.value"
          class="border-0 bg-transparent placeholder:text-text-clr placeholder:opacity-80"
          placeholder="Search Medicine "
        />
        <BaseIcon :path="mdiMagnify" :size="20" />
      </div>

      <div class="flex gap-2 items-center">
        <Button
          type="primary"
          @click="openModal('RegularRequest', selected)"
          class="flex gap-4 px-4 border border-primary rounded h-14 items-center"
        >
          <BaseIcon :path="mdiCartOutline" size="xl" />
          <p class="">Regular Request</p>
        </Button>
        <button
          :disabled="selected.length == 0"
          :class="[!selected.length && 'opacity-20']"
          @click="
            openModal('RegularRequest', selected, () => {
              selected = [];
            })
          "
          class="hover:bg-primary hover:text-white flex gap-4 px-4 border border-primary rounded h-14 items-center"
        >
          <BaseIcon :path="mdiCartOutline" size="xl" />
          <p class="">Emergency Request</p>
        </button>
        <button
          @click="openModal('ViewRespond')"
          class="flex gap-4 bg-primary px-4 text-white border rounded h-14 items-center hover:bg-primary"
        >
          <p class="">Request Status</p>
        </button>
        <Dropdown top="170%" v-slot="{ setRef, toggleDropdown }">
          <BaseIcon @click="toggleDropdown" :path="mdiMenuDown" size="3xl" />
          <div :ref="setRef">
            <div
              class="flex flex-col *:text-left gap-2 p-2 bg-white shadow-lg w-50"
            >
              <button
                @click="router.push('/history')"
                class="flex gap-2 bg-gray-200 truncate px-4 border rounded h-10 items-center hover:bg-primary"
              >
                <BaseIcon :path="mdiHistory" />
                <p class="">History</p>
              </button>
              <button
                @click="router.push('/stock/expireddrugs')"
                class="flex gap-2 bg-gray-200 truncate px-4 border rounded h-10 items-center hover:bg-primary"
              >
                <BaseIcon :path="mdiClockAlertOutline" />
                Expired Drugs
              </button>
            </div>
          </div>
        </Dropdown>
      </div>
    </div>
    <div class="">
      <Table
        v-model="selected"
        :pending="pagination.pending.value"
        :headers="{
          head: [
            'Batch Number',
            'Medicine Name',
            'Dosage form',
            'Dispensary Quantity',
            'Store Quantity',
            'Brand Name',
            'Price',
            'EXP. Date',
            'Actions',
          ],
          row: [
            'batchNumber',
            'drugName',
            'dosageForm',
            'totalAmount',
            'totalStoreAmount',
            'drugBrandName',
            'sellingUnitPrice',
            'expDate',
          ],
        }"
        :rows="
          (stockStore.stock || [])
            .reduce((drugs, drug) => {
              if (stockStore.checkExpirdity(drug.drugUuid)) {
                console.log(drug);
                drugs.unshift(drug);
              } else {
                drugs.push(drug);
              }
              return drugs;
            }, [])
            .map((el) => {
              return {
                dosageForm: `Accurate dose`,
                ...el,
              };
            })
        "
        :cells="{
          totalAmount: PaperPrescriptionStatusCell,
          sellingUnitPrice: (price) => {
            return formatCurrency(price);
          },
          strength: (_, row) => {
            return `${row?.strength ?? ''} ${row?.unit ?? ''}`;
          },
          totalAmount: (_, row) => {
            return `${row?.totalAmount} ${row?.drugUnit}`;
          },
          expDate: StockExDateTableCell,
        }"
        :rowCom="RedTablerow"
      >
        <template #actions="{ row }">
          <Dropdown v-slot="{ setRef, toggleDropdown }">
            <button @click="toggleDropdown" class="rounded-full p-1">
              <BaseIcon :path="mdiDotsVertical" :size="24" />
            </button>
            <div class="flex shadow-lg p-1 border rounded flex-col gap-2 bg-white" :ref="setRef">
              <button
                @click="openModal('ChangeUnitPrice', row)"
                class="flex items-center gap-2 hover:bg-gray-100 p-1 rounded"
              >
                <BaseIcon :path="mdiCurrencyUsd" :size="20" />
                <span>Change Price</span>
              </button>
            </div>
          </Dropdown>
        </template>
      </Table>
    </div>
  </div>
</template>
