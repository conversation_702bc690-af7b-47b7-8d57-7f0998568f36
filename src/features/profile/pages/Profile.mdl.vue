<script setup>
import BaseIcon from '@/components/base/BaseIcon.vue';
import Button from '@/components/Button.vue';
import Input from '@/components/new_form_elements/Input.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import NewFormParent from '@/features/new_admin/role/components/NewFormParent.vue';
import Form from '@/new_form_builder/Form.vue';
import InputParent from '@/new_form_builder/InputParent.vue';
import { mdiCamera } from '@mdi/js';
import { onMounted, ref, watch, computed } from 'vue';
import { editProfile, getProfileById } from '../api/profileApi';
import { Icon } from "@iconify/vue";
import { toasted } from '@/utils/utils';
import { closeModal as cm } from '@customizer/modal-x';
import Spinner from '@/components/Spinner.vue';
import { generateAvatar } from '@/utils/avatarHelper';
import { useForm } from '@/new_form_builder/useForm';


const props = defineProps({
  data: Object
})


console.log(props.data);

const req = useApiRequest()
const getreq = useApiRequest()
const isLoading = ref(true); // Add loading state
const showModal = ref(false); // State variable to control modal visibility
const scale = ref(1); // State variable for zoom level


onMounted(() => {
  isLoading.value = true;
  getreq.send(() => getProfileById(props.data?.userUuid))
})

const profilePicture = ref(localStorage.getItem('profilePicture') || null); // Load from local storage or use default

const selectedFile = ref(null);


const changeProfilePicture = () => {
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = 'image/*';
  fileInput.onchange = (event) => {
    const file = event.target.files[0];
    if (file) {
      selectedFile.value = file; // Store the selected file
      const reader = new FileReader();
      reader.onload = (e) => {
        profilePicture.value = e.target.result; // Update profile picture
        localStorage.setItem('profilePicture', e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };
  fileInput.click();
};
const userData = ref([{ label: 'User Name', value: 'userName', validation: 'required|alpha' }, { label: 'Email', value: 'email', validation: 'required|email' }, { label: 'Mobile Phone', value: 'mobilePhone', validation: 'required|phone' }, { label: 'Gender', value: 'gender', validation: 'required' }])

function openModal() {
  showModal.value = true; // Open the modal
}

function closeModal() {
  showModal.value = false; // Close the modal
}

function zoomIn() {
  scale.value += 0.1; // Increase scale for zoom in
}

function zoomOut() {
  scale.value = Math.max(1, scale.value - 0.1); // Decrease scale for zoom out, minimum 1
}


function changeProfile({ values }) {
  const formData = new FormData();
  formData.append('userUuid', props.data?.userUuid);
  formData.append('userName', values.userName);


  if (selectedFile.value) {
    formData.append('profilePicture', selectedFile.value);
  }
  else {
    formData.append('profilePicture', getreq.response.value.imageData)
  }

  req.send(() => editProfile(props.data?.userUuid, {}, formData),
    (res) => {
      console.log(res); // Debug output
      if (res.success) {
        toasted(res.success, 'Changed Successfully', res.error);
        cm();
      } else {
        console.error('Error:', res.error);
      }
    });
}

// Computed property to get the avatar image
const avatarImage = computed(() => {
  if (selectedFile.value) {
    // If a file has been selected, use the local preview
    return profilePicture.value;
  } else {
    // Otherwise use the helper function to generate an avatar
    return generateAvatar({
      imageData: getreq.response.value?.imageData,
      userName: getreq.response.value?.userName || props.data?.userName,
      userId: props.data?.userUuid,
      roleName: props.data?.roleName
    });
  }
});

watch(getreq.response, () => {
  isLoading.value = false; // Set loading to false when data is fetched
  if (getreq.response.value) {
    userData.value.forEach(el => {
      if (el.value == 'userName') {
        props.data.userName = getreq.response.value.userName
      }
    })

    if (getreq.response.value.imageData) {
      const fetchedImage = `data:image/png;base64,${getreq.response.value.imageData}`;
      profilePicture.value = fetchedImage;
      localStorage.setItem('profilePicture', fetchedImage); // Save to local storage
    }
  }
})

const { submit } = useForm('profile')
</script>

<template>
  <div class="bg-black/50 min-h-full p-10 grid place-items-center">
    <NewFormParent title="Profile" size="md">
      <div class="max-w-[600px] object-cover p-5 grid justify-center">
        <Spinner v-if="getreq.pending.value" />
        <div v-else>
          <div class="flex flex-col items-center gap-2 mb-10 font-bold">
            <img @click="openModal" :src="avatarImage" alt="Profile Picture"
              class="w-[100px] h-[100px] mr-5 rounded-full object-cover cursor-pointer" />
            <BaseIcon @click="changeProfilePicture" class="text-right" :path="mdiCamera" />
            <p :class="{ 'error-class': req.error.value }">{{ req.error.value }}</p>
            <p>{{ props.data?.firstName }} {{ props.data?.fatherName }}</p>
            <p>{{ props.data?.email }}</p>
          </div>
        </div>
        <Form :inner="false" id="profile" class="w-full profile-grid gap-16 mb-5 items-center">
          <div class="flex flex-col gap-8">
            <div v-for="{ label } in userData" :key="label">
              <p>{{ label }}</p>
            </div>
          </div>
          <div class="flex flex-col gap-8">
            <div v-for="{ value, validation } in userData" :key="value">
              <InputParent :validation="validation" v-if="value != 'fullname'" :attributes="{
                placeholder: 'Enter username'
              }" :value="props.data[value]" v-slot='{ setRef, error }' :name="value">
                <div class="flex flex-col">
                  <input :ref="setRef" class="!p-0" />
                  <span v-if="error" class="text-xs text-red-500 truncate">{{ error }}</span>
                </div>
              </InputParent>
              <InputParent v-else v-slot="{ setRef }" name="fullname"
                :value="`${props.data?.firstName} ${props.data?.fatherName}`" validation="required">
                <input :ref="setRef" />
              </InputParent>
            </div>
          </div>
        </Form>
      </div>
      <template #bottom >
        <div class="grid col-span-2">
          <button type="submit" @click.prevent="submit(changeProfile)"
            class="w-full cursor-pointer justify-center text-md flex bg-secondary rounded-md text-white font-bold py-2">
            <div v-if="req.pending.value">
              <Icon icon="svg-spinners:3-dots-scale" class="text-2xl" />
            </div>
            <div v-if="!req.pending.value">Change</div>
          </button>
        </div>
      </template>
    </NewFormParent>

    <!-- Modal for Viewing Profile Picture -->
    <div v-if="showModal" class="fixed inset-0 flex items-center justify-center bg-black">
      <div class="relative">
        <p @click="closeModal" class="text-white absolute -top-3 -right-8 cursor-pointer rounded-full">X</p>
        <div class="bg-white p-4 rounded-lg">
          <img :src="avatarImage" alt="Profile Picture" class="max-w-full max-h-[80vh] rounded-lg"
            :style="{ transform: `scale(${scale})` }" />
          <!-- <button @click="zoomOut" class="bg-gray-300 px-3 py-1 rounded">Zoom Out</button>
      <button @click="zoomIn" class="bg-gray-300 px-3 py-1 rounded">Zoom In</button> -->
          <!-- <button @click="closeModal" class="mt-2 bg-red-500 text-white px-4 py-2 rounded">Close</button> -->
        </div>
      </div>
    </div>

  </div>
</template>

<style>
.profile-grid {
  display: grid;
  grid-template-columns: max-content 1fr;
}

.error-class {
  color: red;
}
</style>