<script setup>
import NewFormParent from '@/features/new_admin/role/components/NewFormParent.vue';
import PatientForm from '@/features/patients/components/form/PatientForm.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import { updatePatient } from '@/features/paper_prescription/api/patientApi';
import { closeModal } from '@customizer/modal-x';
import { toasted } from '@/utils/utils';
import { usePaperPrescriptionStore } from '@/features/paper_prescription/store/paperPrescriptionStore';

const props = defineProps({
	data: Object
});

const req = useApiRequest();
const paperPrescriptionStore = usePaperPrescriptionStore();

function editPatient(values) {
  req.send(
    () => updatePatient(props.data?.userUuid, {
			...props.data,
			...values
		}),
    (res) => {
      if (res.success) {
        paperPrescriptionStore.setPatient({
          ...props.data,
          ...values,
        });
        closeModal();
      }
			toasted(res.success, "Patient Updated Successfully", res.error);
    }
  );
}
</script>

<template>
	<div class="bg-black/50 min-h-full w-full p-4 grid place-items-center">
		<NewFormParent size="lg" title="Edit Patient">
			<div class="gap-10 bg-white rounded-lg flex flex-col">
				<PatientForm
          btnText="Update"
          :petient="props.data"
          :addPatient="editPatient"
          :pending="req.pending.value"
        />
			</div>
		</NewFormParent>
	</div>
</template>