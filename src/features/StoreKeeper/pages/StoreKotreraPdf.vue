<script setup>
import { useApiRequest } from "@/composables/useApiRequest";
import { useRoute } from "vue-router";
import StoreKoteraPdf from "@/components/pdf/StoreKotera.pdf.vue";
import icons from "@/utils/icons";
import { ref } from "vue";
import {
  getKoteraByBatch,
  storeKoteraByBatchId,
} from "../api/dispensaryKoteraApi";
import { formatCurrency, secondDateFormat } from "@/utils/utils";

const route = useRoute();
const batchUuid = route.params?.batchUuid;

const req = useApiRequest();

req.send(
  () => storeKoteraByBatchId(batchUuid),
  (res) => {
    if (res.success) {
      console.log("Fetched kotera data:", res.data);
    } else {
      console.error("Failed to fetch kotera data:", res.error);
    }
  }
);
</script>

<template>
  <div>
    <div v-if="req.pending.value" class="grid place-items-center h-full">
      <i v-html="icons.spinner" class="animate-spin" />
    </div>
    <StoreKoteraPdf
      v-else-if="Array.isArray(req.response.value?.storeKoteraList)"
      :drugs="
        req.response.value?.storeKoteraList?.map((drug, index) => {
          return [
            {
              text: `${index + 1} `,
            },
            {
              text: `${drug?.drugCode || ' '}`,
            },
            {
              text: `${drug?.drugName || ' '}`,
            },
            {
              text: `${drug?.dosageForm || ' '}`,
            },
            {
              text: `${drug?.batchNumber || ' '}`,
            },
            {
              text: `${secondDateFormat(drug?.expDate) || ' '}`,
            },
            {
              text: `${formatCurrency(drug?.unitPrice) || ' '}`,
            },
            {
              text: drug?.countedStoreAmount || ' ',
            },
            {
              text: ' '
            },
            {
              text: `${formatCurrency((drug?.countedStoreAmount || 0) * (drug?.unitPrice || 0))}`,
            },
            {
              text: `${(drug?.countedStoreAmount || 0) - (drug?.totalAmount || 0)}`,
            },
            {
              text: `${drug?.remark || ' '}`,
            },
          ];
        }) || []
      "
    />
  </div>
</template>
