<script setup>
import Button from "@/components/Button.vue";
import Textarea from "@/components/new_form_elements/Textarea.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import NewFormParent from "@/features/new_admin/role/components/NewFormParent.vue";
import { finalizeKotera } from "@/features/paper_prescription/api/paperPrescriptionApi";
import Form from "@/new_form_builder/Form.vue";
import { toasted } from "@/utils/utils";
import { closeModal } from "@customizer/modal-x";
import { useRoute, useRouter } from "vue-router";

const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
});

const router = useRouter();
const req = useApiRequest();

function finalize() {
  if (req.pending.value) return;
  req.send(
    () =>
      finalizeKotera(
        props.data.map((el) => ({ inventoryUuid: el.inventoryUuid }))
      ),
    (res) => {
      toasted(res.success, "Successfull.", res.error);
      if (res.success) {
        closeModal();
        router.push("/dispensary-kotera-history");
      }
    }
  );
}
</script>

<template>
  <div class="bg-black/50 h-55 p-10 min-h-full w-full grid place-items-center">
    <NewFormParent size="xs" title="Generate Kotera Report">
      <Form
        class="grid grid-cols-5 gap-3 pt-5"
        v-slot="{ submit }"
        id="DispensingGenerateReportform"
      >
        <Textarea
          class="col-span-5"
          name="remark"
          label="Remark"
          :attributes="{ placeholder: 'Type your remark here' }"
        ></Textarea>
        <Button @click.prevent="finalize" type="primary" class="self-end"
          >Complete Generating Report</Button
        >
      </Form>
    </NewFormParent>
  </div>
</template>
