<script setup>
import Input from "@/components/new_form_elements/Input.vue";
import Select from "@/components/new_form_elements/Select.vue";
import Form from "@/new_form_builder/Form.vue";
import Button from "@/components/Button.vue";
import { computed, ref } from "vue";
import { getAllManufacturers } from "@/features/new_admin/manufacturer/api/ManufacturerApi";
import { getAllSuppliers } from "@/features/new_admin/supplier/api/SupplierApi";
import SearchSelect from "@/components/SearchSelect.vue";
import Icon from "@/components/Icon.vue";

const props = defineProps({
  disabledWithValue: {
    type: Boolean,
    default: false,
  },
  filter: {
    type: Object,
  },
  pending: Boolean,
  drug: Object,
  onSubmit: {
    type: Function,
  },
});

const _filter = computed(() => {
  return {
    unit: true,
    supplier: true,
    by: true,
    compounding: true,
    ...props.filter,
  };
});

const options = ref([
  "Tablet",
  "Capsule",
  "piece",
  "Liquid",
  "Syrup",
  "strip",
  "Oral Suspension",
  "Lotion",
  "Injection",
  "Ointment",
  "Cream",
  "Other",
]);
const options2 = ref([
  "ml",
  "pair",
  "pc",
  "pcs",
  "pk",
  "roll",
  "sachet",
  "st",
  "st/10",
  "str",
  "tab",
  "tin",
  "tube",
  "vial",
  "5/pk",
  "10/pk",
  "5/st",
  "50/pk",
  "50/st",
  "6/st",
  "60/pk",
  "6tab",
  "7/st",
  "8/st",
  "80/pk",
  "amp",
  "bottle",
  "caplet",
  "gm",
  "kit",
  "Other",
]);

const forCompounding = ref(false); // Defaults to false

const selectedOption2 = ref("");
const selectedOption = ref("");
const otherValue = ref("");
const otherValue2 = ref("");
</script>
<template>
  <Form
    class="grid grid-cols-3 gap-5 pt-5"
    v-slot="{ submit }"
    id="additemform"
  >
    <div class="flex flex-col gap-1">
      <label class="text-sm">Drug Name</label>
      <p
        class="border truncate bg-base-clr-1 rounded-md px-3 h-11 flex place-content-start items-center"
      >
        {{ drug?.drugName || "" }}
      </p>
    </div>
    <Select
      v-if="_filter.unit && selectedOption2 != 'Other'"
      name="bulkUnit"
      label="Unit"
      validation="required"
      :options="options2"
      v-model="selectedOption2"
      :attributes="{
        ...(disabledWithValue
          ? {
              disabled: true,
            }
          : {}),
        type: 'text',
        placeholder: 'Unit',
      }"
    >
    </Select>
    <Input
      v-else-if="_filter.unit"
      v-model="otherValue2"
      name="bulkUnit"
      label="Unit"
      :attributes="{
        placeholder: 'Enter other unit',
      }"
    />
    <div v-if="_filter.by" class="flex flex-col items-center gap-1">
      <p class="text-sm text-left w-full capitalize px-1 truncate">
        {{ "         ." }}
      </p>
      <div class="flex justify-start gap-2">
        <!-- <Input
                name="strength"
                :attributes="{
                  placeholder: 'strength',
                }"
              />
              <Select
                class="w-[4rem] flex items-center"
                name="unit"
                validation="required"
                :options="['g', 'Pcs', 'mg', 'ml']"
                :attributes="{
                  type: 'text',
                  placeholder: 'unit',
                }"
              ></Select>
              <p class="h-11 grid place-items-center">of</p> -->
        <Input
          name="ofX"
          :value="1"
          :attributes="{
            placeholder: 'of X',
          }"
        />
        <p class="h-11 grid place-items-center">by</p>
        <Input
          name="byY"
          :value="1"
          :attributes="{
            placeholder: 'By Y',
          }"
        />
      </div>
    </div>
    <Input
      name="batchNumber"
      label="Batch Number"
      validation="required"
      :attributes="{
        placeholder: 'Batch Number',
      }"
    />
    <Input
      :value="drug?.unitPrice"
      name="buyingUnitPrice"
      label="Unit Price"
      validation="required"
      :attributes="{
        ...(disabledWithValue
          ? {
              disabled: true,
            }
          : {}),
        placeholder: 'Unit price',
      }"
    />
    <Input
      name="boughtAmount"
      label="Quantity"
      :validation="{
        required: true,
        ...(!!drug
          ? {
              num_minmax: {
                args: [1, drug.quantity],
              },
            }
          : {}),
      }"
      :attributes="{
        placeholder: 'Quantity',
      }"
    />
    <div class="flex flex-col gap-2">
      <Select
        :value="drug?.unit"
        v-if="selectedOption != 'Other'"
        name="dispensingUnit"
        label="Dispensing Unit"
        validation="required"
        v-model="selectedOption"
        :options="options"
        :attributes="{
          ...(disabledWithValue
            ? {
                disabled: true,
              }
            : {}),
          type: 'text',
          placeholder: 'Dispensing Unit',
        }"
      />
      <Input
        v-else
        v-model="otherValue"
        name="dispensingUnit"
        label="Dispensing Unit"
        :attributes="{
          placeholder: 'Enter other dispensing unit',
        }"
      />
    </div>
    <Select
      :attributes="{ placeholder: 'Select Brand', type: 'text' }"
      :obj="true"
      label="Brand"
      name="brandUuid"
      :options="
        (drug?.brand || []).map((el) => ({
          label: el.drugBrandName,
          value: el.drugBrandUuid,
        }))
      "
    />
    <SearchSelect
      v-if="_filter.supplier"
      :value="drug?.supplierName"
      position="left-bottom"
      label="Supplier"
      :option="{ label: 'supplierName', value: 'supplierUuid' }"
      :search-cb="getAllSuppliers"
      placeholder="Search Supplier"
    />
    <SearchSelect
      position="left-bottom"
      label="Manufacturer"
      :option="{ label: 'manufacturerName', value: 'manufacturerUuid' }"
      :search-cb="getAllManufacturers"
      placeholder="Search for Manufacturere"
    />
    <Input
      name="expirationDate"
      label="EXP. Date"
      validation="required"
      :attributes="{
        type: 'date',
        placeholder: 'exp. date',
      }"
    />
    <Input
      v-if="forCompounding && _filter.compounding"
      name="strength"
      label="Strength"
      validation="required|num"
      :attributes="{
        placeholder: 'Strength',
      }"
    />
    <div
      v-if="_filter.compounding"
      class="col-span-3 pt-4 flex items-center gap-2"
    >
      <input
        type="checkbox"
        id="forCompounding"
        v-model="forCompounding"
        name="forCompounding"
        class="w-4 h-4"
      />
      <label for="forCompounding" class="text-md font-bold"
        >For Compounding</label
      >
    </div>

    <div class="w-full flex flex-col pt-10 col-span-3 justify-end">
      <div class="flex flex-col w-full justify-end">
        <Button
          :pending="pending"
          @click.prevent="
            submit(({ values }) =>
              onSubmit ? onSubmit({ forCompounding, ...(drug || {}), ...values }) : () => {}
            )
          "
          class="w-full cursor-pointer justify-center text-md flex bg-primary rounded-md text-white font-bold py-3"
        >
          <div v-if="pending">
            <Icon icon="svg-spinners:3-dots-scale" class="text-2xl" />
          </div>
          <div v-if="!pending">Add</div>
        </Button>
      </div>
    </div>
  </Form>
</template>
