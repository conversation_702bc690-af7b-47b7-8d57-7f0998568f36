<script setup lang="ts">
import FormSubmitButton from '@/components/FormSubmitButton.vue';
import Input from '@/components/new_form_elements/Input.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import NewFormParent from '@/features/new_admin/role/components/NewFormParent.vue';
import { allowRequstedTrasnsfer } from '@/features/stock/api/stockApi';
import Form from '@/new_form_builder/Form.vue';
import { useRequestedStore } from '../store/RequestedStore';
import { toasted } from '@/utils/utils';
import { closeModal, openModal } from '@customizer/modal-x';
const props = defineProps({
    data: Object
})
const transferStore = useRequestedStore()
const req = useApiRequest()
function allowTransfer({ values }) {
    openModal('Confirmation', {
        title: 'Transfer',
        message: 'Are you sure you want to transfer?'
    },
        (res) => {
            if (res) {
                req.send(
                    () => allowRequstedTrasnsfer(props.data.stockTransferUuid, {
                        approvedAmount: values.approvedAmount
                    }),
                    (res) => {
                        if (res.success) {
                            transferStore.updateQuantity(props.data.requestedByUuid, values);
                        }
                        toasted(res.success, 'Transfered Successfully', res.error);
                        closeModal();
                    })
            }
        })
}
</script>


<template>
    <div class="bg-black/50 h-55 p-10 min-h-full  grid place-items-center">
        <NewFormParent size='xs' title="Add Transfer">
            <Form class="grid grid-cols-2 gap-3 pt-5" v-slot="{ submit }" id="transferform">
                <div class="flex flex-col gap-1">
                    <label class="text-sm">Drug Name</label>
                    <p class="border bg-base-clr-1 rounded-md px-3 h-11 flex place-content-start items-center "> {{
                        props.data?.drugName || '' }}
                    </p>
                </div>
                <Input label=" Approved Amount" name="approvedAmount" :attributes="{
                    placeholder: ' approvedAmount',
                }" validation="required" />
                <div class="w-full flex pt-10 col-span-3 justify-end">
                    <div class="flex w-[8rem] justify-end">
                        <FormSubmitButton @click.prevent="submit(allowTransfer)"
                            class="col-span-2 font-bold rounded bg-secondary" btn-text="Transfer" />
                    </div>
                </div>
            </Form>
        </NewFormParent>
    </div>

</template>