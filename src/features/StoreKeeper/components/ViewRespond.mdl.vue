<script setup>
import Table from "@/components/Table.vue";
import { usePaginationcopy } from "@/composables/usePaginationcopy";
import NewFormParent from "@/features/new_admin/role/components/NewFormParent.vue";
import {
  getApprovedTrasnsfer,
  getMedicineByStatus,
  recievedRequstedTrasnsfer,
} from "@/features/stock/api/stockApi";
import TableRowSkeleton from "@/skeletons/TableRowSkeleton.vue";
import Button from "@/components/Button.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import {
  BatchStatus,
  openNewTab,
  RequestOptions,
  secondDateFormat,
  toasted,
} from "@/utils/utils";
import { ref, watch } from "vue";
import { useTransferStore } from "../store/TransferStore";
import { useStockStore } from "../store/StockStore";
import {
  getAllRequest,
  getRequestByBatch,
  updateRequestedBatch,
} from "@/features/stock/api/regularRequestApi";
import { usePagination } from "@/composables/usePagination";
import { openModal } from "@customizer/modal-x";

const approvedStore = useTransferStore();
const stockStore = useStockStore();

// const pagination = usePaginationcopy({
//     cb: getApprovedTrasnsfer
// })
const req = useApiRequest();

const options = ref([
  { lebel: "APPROVED", value: BatchStatus.ISSUED },
  { lebel: "REQUESTED", value: BatchStatus.REQUESTED },
]);

let option = ref(BatchStatus.ISSUED);
const approvedPagin = usePagination({
  store: approvedStore,
  cb: (params) =>
    getAllRequest({
      ...params,
      status: option.value,
    }),
});

watch(option, () => {
  approvedPagin.send();
});

function approveRequest(row) {
  console.log(row);
  req.send(
    () =>
      recievedRequstedTrasnsfer(row.stockTransferUuid, {
        approvedAmount: row.approvedAmount,
      }),
    (res) => {
      if (res.success) {
        stockStore.updateQuantity(row.toInventoryUuid, {
          approvedAmount: row.approvedAmount,
        });
      }
      toasted(res.success, "Recieved Successfully", res.error);
    }
  );
}

const baseUrl = import.meta.env?.v_BASE_URL;

const bacthReq = useApiRequest();
function reciveBatch(row) {
  if (bacthReq.pending.value) return;

  bacthReq.send(
    () =>
      updateRequestedBatch(
        row?.batchUuid,
        row?.inventoryResponses?.map((el) => {
          return {
            approvedAmount: el.approvedAmount,
            toInventoryUuid: el.toInventoryUuid,
            remark: el.remark,
          };
        }),
        {
          status: BatchStatus.RECEIVED,
        }
      ),
    (res) => {
      if (res.success) {
        approvedStore.remove(row?.batchUuid);
      }
      toasted(res.success, "Successfull!", res.error);
    }
  );
}
</script>

<template>
  <div class="bg-black/50 p-8 min-h-full w-full grid place-items-center">
    <NewFormParent class="p-8" size="xl" title="Approve Transfer">
      <div class="flex flex-col gap-4">
        <select
          v-model="option"
          class="self-end p-4 w-30 bg-secondary text-white"
        >
          <option
            :value="option.value"
            :key="option.lebel"
            v-for="option in options"
          >
            {{ option.lebel }}
          </option>
        </select>
        <Table
          :pending="approvedPagin.pending.value"
          :headers="{
            head: ['Request Sent Date', 'Status', 'Actions'],
            row: ['sentDate', 'status'],
          }"
          :rows="approvedStore.stock"
          :cells="{
            status: () => option,
            sentDate: secondDateFormat,
          }"
        >
          <template #actions="{ row }">
            <div class="flex gap-2">
              <Button
                @click.stop="
                  openModal('ApprovalRequest', {
                    batchUuid: row?.batchUuid,
                    drugs: row?.inventoryResponses,
                    editing: false,
                  })
                "
                type="secondary"
                size="xs"
              >
                View Detail
              </Button>
              <div v-if="option == BatchStatus.ISSUED" class="flex gap-2">
                <Button
                  @click="openNewTab(`${baseUrl}/model22/${row?.batchUuid}`)"
                  type="secondary"
                  size="xs"
                  v-if="option == BatchStatus.ISSUED"
                  >model22</Button
                >
                <Button
                  @click.prevent="reciveBatch(row)"
                  type="primary"
                  size="xs"
                  >Recieve</Button
                >
              </div>
            </div>
          </template>
        </Table>
      </div>
    </NewFormParent>
  </div>
</template>
