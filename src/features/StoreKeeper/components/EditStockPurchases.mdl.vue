<script setup lang="ts">
import Input from '@/components/new_form_elements/Input.vue';
import { useApiRequest } from '@/composables/useApiRequest';
import NewFormParent from '@/features/new_admin/role/components/NewFormParent.vue';
import Form from '@/new_form_builder/Form.vue';
import { updateSrv } from '../api/InventoryApi';
import { usePurchasesStore } from '../store/PurchasesStore';
import { toasted } from '@/utils/utils';
import { Icon } from "@iconify/vue";
import { closeModal } from '@customizer/modal-x';
import { ref } from 'vue';
const props = defineProps({
    data: Object
})
console.log(props.data);
const purchaseStore = usePurchasesStore()
const req = useApiRequest()

function update({ values }) {
    req.send(() => updateSrv(props.data?.stockPurchaseUuid, {
        drugUuid: props.data?.drugUuid,
        brandUuid: props.data?.drugBrandUuid,
        purchaseOrderUuid: props.data?.purchaseOrderUuid,
        ...values,
        forCompounding: forCompounding.value,
    }),
        (res) => {
            if (res.success) {
                purchaseStore.update(props.data?.stockPurchaseUuid, { ...(props.data || {}), ...values })
            }
            toasted(res.success, 'Successfully Updated', res.error);
            closeModal();
        })
}
const forCompounding = ref(false); // Defaults to false

</script>

<template>
    <div class="bg-black/50 h-55 p-10 min-h-full  grid place-items-center">
        <NewFormParent size='xs' title="Edit Puchases">
            <div class="w-[40rem]">
                <Form class="grid grid-cols-2 gap-5 pt-5" v-slot="{ submit }" id="purchaseform">
                    <div class="flex flex-col gap-1">
                        <label class="text-sm">Drug Name</label>
                        <p class="border bg-base-clr-1 rounded-md px-3 h-11 flex place-content-start items-center "> {{
                            props.data?.drugName || '' }}
                        </p>
                    </div>
                    <Input name="batchNumber" label="Batch Number" :value="props.data?.batchNumber"
                        validation="required" :attributes="{
                            placeholder: 'Batch Number'
                        }" />
                    <Input name="buyingUnitPrice" label="Buying Unit Price" :value="props.data?.buyingUnitPrice"
                        validation="required" :attributes="{
                            placeholder: 'Buying Unitprice'
                        }" />
                    <Input name="boughtAmount" label="Bought Amount" :value="props.data?.boughtAmount"
                        validation="required" :attributes="{
                            placeholder: 'Boughtm Amount'
                        }" />
                    <Input name="expirationDate" label="EXP. Date" :value="props.data?.expirationDate"
                        validation="required" :attributes="{
                            type: 'date',
                            placeholder: 'exp. date'
                        }" />
                    <div class="col-span-2 pt-4 flex items-center gap-2">
                        <input type="checkbox" id="forCompounding" v-model="forCompounding" name="forCompounding"
                            class="w-4 h-4">
                        <label for="forCompounding" class="text-md font-bold">For Compounding</label>
                    </div>
                    <button type="submit" @click.prevent="submit(update)"
                        class="w-full cursor-pointer justify-center text-md col-span-2 bg-primary rounded-md text-white font-bold py-2">
                        <div v-if="req.pending.value">
                            <Icon icon="svg-spinners:3-dots-scale" class="text-2xl text-center" />
                        </div>
                        <div v-if="!req.pending.value">Update</div>
                    </button>
                </Form>

            </div>
        </NewFormParent>
    </div>
</template>