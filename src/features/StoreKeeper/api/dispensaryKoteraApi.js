import ApiService from "@/service/ApiService";

const api = new ApiService()

const path = '/inventory/kotera'
const path2 = "/store-kotera"

export function dispensaryKoteraHistory(query = {}) {
	return api.addAuthenticationHeader().get(`${path}/batch/all`, {
		params: query
	})
}

export function storeKoteraHistory(query = {}) {
	return api.addAuthenticationHeader().get(`${path2}/batch`, {
		params: query
	})
}


export function storeKoteraByBatchId(id) {
	return api.addAuthenticationHeader().get(`${path2}/batch/${id}`)
}

export function getKoteraByBatch(batchUuid) {
  return api.addAuthenticationHeader().get(`${path}/batch/${batchUuid}`);
}