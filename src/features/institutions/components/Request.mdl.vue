<script setup>
import { ModalParent, closeModal } from "@customizer/modal-x";
import BaseIcon from "@/components/base/BaseIcon.vue";
import { mdiClose } from "@mdi/js";
import NewFormModal from "@/components/NewFormModal.vue";
import RequestForm from "./RequestForm.vue";
import NewFormParent from "@/features/new_admin/role/components/NewFormParent.vue";
import { useApiRequest } from "@/composables/useApiRequest";
import { data } from "autoprefixer";

const props = defineProps({
    data: Object
})
console.log(props.data);

</script>
<template>
    <div class="bg-black/50 min-h-full w-full p-10 grid place-items-center">
        <NewFormParent size="xs" title="Request">
            <div class="p-8 gap-10 bg-white rounded-lg flex flex-col">
                <RequestForm :drug="props.data" />
            </div>
        </NewFormParent>
    </div>
</template>
