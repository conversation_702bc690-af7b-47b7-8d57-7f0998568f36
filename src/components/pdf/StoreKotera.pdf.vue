<script setup>
import { ref, toRaw } from "vue";
import PdfMaker from "../PdfMaker.vue";
import { formatCurrency, secondDateFormat } from "@/utils/utils";

const props = defineProps({
  date: String,
  drugs: {
    type: Array,
    required: true,
  },
});

console.log(props.drugs);
const con = ref({});
const generatePdf = () => {
  // Convert reactive data to plain objects to avoid proxy issues
  let plainDrugs = [];
  try {
    if (props.drugs && Array.isArray(props.drugs)) {
      plainDrugs = JSON.parse(JSON.stringify(props.drugs)).map((drug) => {
        if (Array.isArray(drug)) {
          return drug.map((cell) => {
            if (cell && typeof cell === "object") {
              return { ...cell };
            }
            return cell || { text: "" };
          });
        }
        return drug;
      });
    }

    console.log(plainDrugs);
  } catch (error) {
    console.error("Error processing drugs data:", error);
    plainDrugs = [];
  }

  const docDefinition = {
    pageMargins: 5,
    content: [
      { text: "የመጋዘን ቆጠራ ቅጽ", bold: true, fontSize: 8 },
      { text: "Store Inventory Form (SIF)", bold: true, fontSize: 8 },
      {
        columnGap: 10,
        columns: [
          {
            text: "Branch Name:  _________",
          },
          {
            text: "Date: " + props.date,
          }
          
        ],
      },
      { text: " " },
      {
        style: { fontSize: 6 },
        table: {
          headerRows: 2,
          widths: [
            "auto",
            "*",
            "auto",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
          ],
          body: [
            [
              {
                text: "SN",
                rowSpan: 2,
              },
              {
                text: "To be Filled Before the Physical Inventory",
                colSpan: 6,
              },
              {},
              {},
              {},
              {},
              {},
              {
                text: "To be filled during Inventory",
                colSpan: 2,
              },
              {},
              {
                text: "To be Fillied after Physical Inventory",
                colSpan: 2,
              },
              {},
              {
                text: "Remark",
                rowSpan: 2,
              },
            ],
            [
              {},
              {
                text: "Drug Code",
              },
              {
                text: "Description (Drug name, dosage form, strength and brand)",
              },
              { text: "Unit" },
              { text: "Batch No" },
              { text: "Expired Date" },
              {
                text: "Unit Cost",
              },
              {
                text: "Physical Qty",
                alignment: "center",
              },
              { text: "Bin/Stock card Balance" },
              {
                text: "Total Cost",
              },
              { text: "Discrepancy" },
              {},
            ],
            ...[...plainDrugs],
          ],
        },
        layout: {
          hLineWidth: () => 0.5,
          vLineWidth: () => 0.5,
          hLineColor: () => "black",
          vLineColor: () => "black",
          paddingTop: () => 8,
          paddingBottom: () => 8,
        },
      },
      {
        columns: [
          {
            text: "Inventory Registered by Name ",
            width: "33%",
          },
          {
            text: "Counted by Name ",
            width: "33%",
          },
          {
            text: "Recounted by Name ",
            width: "34%",
          },
        ],
      },
      {
        columns: [
          {
            text: "Signature",
            width: "33%",
          },
          {
            text: "Signature ",
            width: "33%",
          },
          {
            text: "Signature ",
            width: "34%",
          },
        ],
      },
      { text: " " },
      {
        text: "Responsible Name   , ,  Persons",
      },
      {
        text: "Signature's    , , ",
      },
    ],
  };

  con.value = docDefinition;
};

generatePdf();
</script>

<template>
  <PdfMaker v-if="con" :content="con" />
</template>
