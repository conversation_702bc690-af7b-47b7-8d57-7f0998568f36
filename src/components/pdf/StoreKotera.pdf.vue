<script setup>
import { ref } from "vue";
import PdfMaker from "../PdfMaker.vue";
import { formatCurrency, secondDateFormat } from "@/utils/utils";

const props = defineProps({
  drugs: {
    type: Array,
    required: true,
  },
})

console.log(props.drugs)
const con = ref({});
const generatePdf = () => {
  const docDefinition = {
    pageMargins: 10,
    content: [
      { text: "የመጋዘን ቆጠራ ቅጽ", bold: true, fontSize: 8 },
      { text: "Store Inventory Form (SIF)", bold: true, fontSize: 8 },
      {
        columns: [
          {
            width: "auto",
            text: "Name of Health facility:  _________",
          },
          {
            width: "auto",

            text: "Section (unit) store: ________ Page number: _______",
          },
          {
            width: "auto",
            text: "Date: _",
          },
        ],
      },
      { text: " " },
      {
        style: { fontSize: 6 },
        table: {
          headerRows: 2,
          widths: [
            "auto",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
            "*",
          ],
          body: [
            [
              {
                text: "SN",
                rowSpan: 2,
              },
              {
                text: "To be Filled Before the Physical Inventory",
                colSpan: 6,
              },
              {},
              {},
              {},
              {},
              {},
              {
                text: "To be filled during Inventory",
								colSpan: 2,
              },
							{},
              {
                text: "To be Fillied after Physical Inventory",
                colSpan: 3,
              },
              {},
              {},
              {
                text: "Remark",
                rowSpan: 2,
              },
            ],
            [
              {},
              {
                text: "Drug Code",
              },
              {
                text: "Description (Drug name, dosage form, strength and brand)",
              },
              { text: "Unit" },
              { text: "Batch No" },
							{ text: "Expired Date" },
              {
                text: "Unit Cost",
              },
              {
                text: "Pysical Qty",
                alignment: "center",
              },
              {text: 'Bin/Stock card Balance',},
              {
								text: "Total Cost",
                colSpan: 2,
              },
              {},
              {text: 'Discrepancy'},
							{}
            ],
            ...props.drugs,
          ],
        },
        layout: {
          hLineWidth: () => 0.5,
          vLineWidth: () => 0.5,
          hLineColor: () => "black",
          vLineColor: () => "black",
          paddingTop: () => 8, // Increased padding for taller rows
          paddingBottom: () => 8, // Increased padding for taller rows
        },
      },
      {
        columns: [
          {
            text: "Inventory Registered by Name ",
            width: "33%",
          },
          {
            text: "Counted by Name ",
            width: "33%",
          },
          {
            text: "Recounted by Name ",
            width: "34%",
          },
        ],
      },
      {
        columns: [
          {
            text: "Signature",
            width: "33%",
          },
          {
            text: "Signature ",
            width: "33%",
          },
          {
            text: "Signature ",
            width: "34%",
          },
        ],
      },
      { text: " " },
      {
        text: "Responsible Name   , ,  Persons",
      },
      {
        text: "Signature's    , , ",
      },
    ],
  };

  con.value = docDefinition;
};

generatePdf();
</script>

<template>
  <PdfMaker v-if="con" :content="con" />
</template>
